import subprocess
import sys

def install_packages():
    """安装必要的包"""
    packages = [
        "pandasai",
        "pandas",
        "matplotlib",
        "numpy",
        "requests"
    ]
    
    print("开始安装必要的Python包...")
    for package in packages:
        print(f"正在安装 {package}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"{package} 安装成功!")
        except Exception as e:
            print(f"{package} 安装失败: {str(e)}")
    
    print("所有包安装完成!")

if __name__ == "__main__":
    install_packages()
