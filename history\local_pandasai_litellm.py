"""
本模块提供通过LiteLLM与本地大模型交互的PandasAI功能扩展
"""
import os
import sys
import pandas as pd
import matplotlib.pyplot as plt
import importlib.metadata
import warnings

# 尝试获取pandasai版本
try:
    pandasai_version = importlib.metadata.version('pandasai')
    print(f"已检测到PandasAI版本: {pandasai_version}")
except importlib.metadata.PackageNotFoundError:
    pandasai_version = "unknown"
    warnings.warn("无法确定PandasAI版本，可能导致兼容性问题")

# 根据版本导入相应模块
try:
    if pandasai_version.startswith('3'):
        # PandasAI 3.x版本
        from pandasai import Agent
        from pandasai import DataFrame as PAIDataFrame
    else:
        # PandasAI 2.x或其他版本
        from pandasai import SmartDataframe as PAIDataFrame
        from pandasai import PandasAI as Agent
    from pandasai_litellm import LiteLLM
except ImportError as e:
    print(f"导入PandasAI模块失败: {str(e)}")
    print("请确保已安装pandasai和pandasai-litellm包")
    print("可以使用以下命令安装: pip install 'pandasai>=2.0' pandasai-litellm")
    sys.exit(1)

# LiteLLM PandasAI集成类
class PandasAILiteLLMHelper:
    """基于LiteLLM的PandasAI助手类"""
    
    def __init__(self, model_name="ollama/qwen2.5-coder:7b", api_base="http://localhost:11434", 
                 temperature=0, max_tokens=2000):
        """
        初始化PandasAI LiteLLM助手
        
        参数:
            model_name (str): 模型名称，格式为'ollama/模型名'
            api_base (str): Ollama API地址
            temperature (float): 温度参数，控制输出随机性
            max_tokens (int): 最大生成token数
        """
        self.model_name = model_name
        self.api_base = api_base
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.agent = None
        self.version = pandasai_version
        
    def initialize(self, df=None):
        """
        初始化PandasAI Agent
        
        参数:
            df: 数据框或数据框列表
        
        返回:
            bool: 初始化是否成功
        """
        try:
            # 创建LiteLLM实例
            llm = LiteLLM(
                model=self.model_name,
                api_base=self.api_base,
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )
            
            # 根据不同版本初始化
            if self.version.startswith('3'):
                # PandasAI 3.x版本处理方式
                if isinstance(df, list):
                    # 如果是数据框列表，则需要转换每个dataframe
                    dataframes = [self._ensure_pandasai_dataframe(d) for d in df]
                elif df is not None:
                    # 如果是单个数据框，转为列表
                    dataframes = [self._ensure_pandasai_dataframe(df)]
                else:
                    # 如果没有数据框，使用空列表
                    dataframes = []
                
                # 创建Agent实例
                self.agent = Agent(dataframes, config={"llm": llm})
            else:
                # PandasAI 2.x处理方式
                if isinstance(df, list):
                    # 处理dataframe列表
                    dataframes = [self._ensure_pandasai_dataframe(d) for d in df if d is not None]
                elif df is not None:
                    # 单个dataframe
                    dataframes = [self._ensure_pandasai_dataframe(df)]
                else:
                    dataframes = []
                
                # 创建PandasAI实例
                self.agent = Agent(llm)
                # 将dataframes添加到agent中
                self.dataframes = dataframes
                
            return True
        except Exception as e:
            print(f"初始化PandasAI LiteLLM失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
    
    def process_query(self, df, query):
        """
        处理自然语言查询
        
        参数:
            df: 数据框或数据框列表
            query: 查询字符串
        
        返回:
            (result, error): 查询结果和错误信息
        """
        if self.agent is None:
            if not self.initialize(df):
                return None, "初始化PandasAI代理失败"
            
        # 确保agent已初始化
        if self.agent is None:
            return None, "无法初始化PandasAI代理"
        
        try:
            # 根据不同版本处理查询
            if self.version.startswith('3'):
                # PandasAI 3.x处理方式
                result = self.agent.chat(query)
            else:
                # PandasAI 2.x处理方式
                if hasattr(self, 'dataframes') and self.dataframes:
                    if len(self.dataframes) == 1:
                        result = self.agent.run(self.dataframes[0], query)
                    else:
                        result = self.agent.run(self.dataframes, query)
                else:
                    return None, "没有可用的数据框"
                    
            return result, None
        except Exception as e:
            return None, f"处理查询时出错: {str(e)}"

    def _ensure_pandasai_dataframe(self, df):
        """
        确保数据框是PandasAI DataFrame类型
        
        参数:
            df: pandas.DataFrame或其他格式的数据框
            
        返回:
            pandasai.DataFrame或SmartDataframe实例
        """
        try:
            if df is None:
                return None
            
            # 如果已经是PandasAI DataFrame，直接返回
            if isinstance(df, PAIDataFrame):
                return df
                
            # 如果是pandas DataFrame，转换为PandasAI DataFrame
            if isinstance(df, pd.DataFrame):
                return PAIDataFrame(df)
                
            # 其他情况尝试转换
            return PAIDataFrame(df)
        except Exception as e:
            print(f"转换DataFrame失败: {str(e)}")
            # 转换失败时，尝试直接使用原始dataframe
            return df

    @staticmethod
    def get_available_models(api_base="http://localhost:11434"):
        """
        获取Ollama可用模型列表
        
        参数:
            api_base: Ollama API地址
            
        返回:
            list: 可用模型列表
        """
        import requests
        
        try:
            response = requests.get(f"{api_base}/api/tags")
            if response.status_code == 200:
                result = response.json()
                if 'models' in result and isinstance(result['models'], list):
                    # 提取模型名称
                    models = [f"ollama/{model.get('name')}" for model in result['models'] if 'name' in model]
                    return models
            return []
        except Exception as e:
            print(f"获取模型列表失败: {str(e)}")
            return [] 