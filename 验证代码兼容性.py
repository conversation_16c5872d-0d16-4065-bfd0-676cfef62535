#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证local_ai_fixedV0.7.py与最新专业知识库.json的兼容性
检查代码是否需要更新以支持优化后的知识库结构
"""

import json
import os
import re

def load_knowledge_base():
    """加载专业知识库"""
    try:
        with open('专业知识库.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ 专业知识库.json文件未找到")
        return None

def load_config():
    """加载分类配置"""
    try:
        with open('ai_categorize_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
            return config['分类配置']['问题原因分类']
    except FileNotFoundError:
        print("❌ ai_categorize_config.json文件未找到")
        return None

def analyze_code_structure():
    """分析代码结构，检查知识库使用情况"""
    try:
        with open('local_ai_fixedV0.7.py', 'r', encoding='utf-8') as f:
            code_content = f.read()
    except FileNotFoundError:
        print("❌ local_ai_fixedV0.7.py文件未找到")
        return None
    
    # 检查知识库相关函数
    functions_to_check = [
        '_load_knowledge_base',
        '_rule_based_pre_classification', 
        '_enhance_text_with_knowledge',
        '_calculate_confidence_score',
        '_validate_classification_result',
        '_generate_problem_cause_prompt'
    ]
    
    found_functions = {}
    for func in functions_to_check:
        pattern = rf'def {func}\('
        if re.search(pattern, code_content):
            found_functions[func] = True
        else:
            found_functions[func] = False
    
    return found_functions, code_content

def check_knowledge_base_structure(knowledge_base):
    """检查知识库结构是否符合代码期望"""
    print("🔍 检查知识库结构...")
    
    required_sections = [
        "元器件知识库",
        "工艺术语库", 
        "分类规则库",
        "专业术语解释",
        "技术关键词库"
    ]
    
    missing_sections = []
    for section in required_sections:
        if section not in knowledge_base:
            missing_sections.append(section)
        else:
            print(f"  ✅ {section}: 存在")
    
    if missing_sections:
        print(f"  ❌ 缺失部分: {missing_sections}")
        return False
    
    # 检查分类规则库结构
    rules = knowledge_base.get("分类规则库", {})
    if "关键词映射" not in rules:
        print("  ❌ 分类规则库缺少'关键词映射'")
        return False
    else:
        mapping_count = len(rules["关键词映射"])
        print(f"  ✅ 关键词映射: {mapping_count}个分类")
    
    return True

def check_code_compatibility(code_content, knowledge_base):
    """检查代码与知识库的兼容性"""
    print("\n🔍 检查代码兼容性...")
    
    # 检查知识库访问模式
    access_patterns = [
        r'knowledge_base\.get\("元器件知识库"',
        r'knowledge_base\.get\("工艺术语库"',
        r'knowledge_base\.get\("分类规则库"',
        r'knowledge_base\.get\("专业术语解释"'
    ]
    
    compatibility_issues = []
    
    for pattern in access_patterns:
        if not re.search(pattern, code_content):
            section = pattern.split('"')[1]
            compatibility_issues.append(f"代码中未找到对'{section}'的访问")
    
    # 检查关键词映射访问
    if not re.search(r'关键词映射', code_content):
        compatibility_issues.append("代码中未找到对'关键词映射'的访问")
    
    if compatibility_issues:
        print("  ❌ 发现兼容性问题:")
        for issue in compatibility_issues:
            print(f"    - {issue}")
        return False
    else:
        print("  ✅ 代码与知识库结构兼容")
        return True

def test_rule_engine_simulation(knowledge_base, config):
    """模拟测试规则引擎功能"""
    print("\n🧪 模拟测试规则引擎...")
    
    # 获取关键词映射
    rules = knowledge_base.get("分类规则库", {}).get("关键词映射", {})
    categories = config.get("类别", [])
    
    # 测试样本
    test_cases = [
        "电容C15耐压不足导致击穿",
        "需求分析阶段遗漏重要功能", 
        "工艺参数设置错误",
        "人员培训不到位"
    ]
    
    success_count = 0
    for test_text in test_cases:
        text_lower = test_text.lower()
        best_category = None
        best_score = 0
        
        # 模拟规则引擎分类
        for category, keywords in rules.items():
            score = sum(1 for keyword in keywords if keyword.lower() in text_lower)
            if score > best_score:
                best_score = score
                best_category = category
        
        if best_category and best_category in categories:
            print(f"  ✅ '{test_text[:20]}...' -> {best_category}")
            success_count += 1
        else:
            print(f"  ❌ '{test_text[:20]}...' -> 无法分类")
    
    success_rate = success_count / len(test_cases) * 100
    print(f"  规则引擎模拟成功率: {success_rate:.1f}%")
    
    return success_rate >= 75

def test_knowledge_enhancement_simulation(knowledge_base):
    """模拟测试知识增强功能"""
    print("\n🧪 模拟测试知识增强...")
    
    # 测试文本
    test_text = "MOSFET选型错误导致回流焊工艺失效"
    
    # 模拟元器件识别
    components = knowledge_base.get("元器件知识库", {})
    identified_components = []
    
    for category, items in components.items():
        if isinstance(items, dict):
            for subcategory, component_list in items.items():
                for component in component_list:
                    if component.lower() in test_text.lower():
                        identified_components.append(f"{component}({subcategory})")
    
    # 模拟工艺术语识别
    processes = knowledge_base.get("工艺术语库", {})
    identified_processes = []
    
    for category, items in processes.items():
        if isinstance(items, dict):
            for subcategory, process_list in items.items():
                for process in process_list:
                    if process.lower() in test_text.lower():
                        identified_processes.append(f"{process}({subcategory})")
    
    print(f"  测试文本: {test_text}")
    print(f"  识别的元器件: {identified_components}")
    print(f"  识别的工艺: {identified_processes}")
    
    if identified_components or identified_processes:
        print("  ✅ 知识增强功能正常")
        return True
    else:
        print("  ❌ 知识增强功能异常")
        return False

def generate_compatibility_report(knowledge_base, config, code_functions, compatibility_results):
    """生成兼容性报告"""
    print("\n" + "="*60)
    print("代码兼容性检查报告")
    print("="*60)
    
    # 1. 文件状态
    print("\n📁 文件状态:")
    print(f"  专业知识库.json: {'✅ 存在' if knowledge_base else '❌ 缺失'}")
    print(f"  ai_categorize_config.json: {'✅ 存在' if config else '❌ 缺失'}")
    print(f"  local_ai_fixedV0.7.py: {'✅ 存在' if code_functions else '❌ 缺失'}")
    
    # 2. 代码函数检查
    if code_functions:
        print("\n🔧 关键函数检查:")
        for func, exists in code_functions.items():
            status = "✅ 存在" if exists else "❌ 缺失"
            print(f"  {func}: {status}")
    
    # 3. 知识库结构
    if knowledge_base:
        print("\n📚 知识库结构:")
        sections = ["元器件知识库", "工艺术语库", "分类规则库", "专业术语解释", "技术关键词库"]
        for section in sections:
            if section in knowledge_base:
                if section == "分类规则库":
                    mapping_count = len(knowledge_base[section].get("关键词映射", {}))
                    print(f"  {section}: ✅ 存在 ({mapping_count}个分类映射)")
                else:
                    print(f"  {section}: ✅ 存在")
            else:
                print(f"  {section}: ❌ 缺失")
    
    # 4. 兼容性测试结果
    print("\n🧪 兼容性测试:")
    for test_name, result in compatibility_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    # 5. 总体评估
    all_tests_passed = all(compatibility_results.values())
    all_functions_exist = all(code_functions.values()) if code_functions else False
    
    print(f"\n📊 总体评估:")
    if knowledge_base and config and all_functions_exist and all_tests_passed:
        print("  🎉 完全兼容！代码无需更新")
        print("  ✅ 所有功能都能正常使用最新的专业知识库")
    elif knowledge_base and config and all_functions_exist:
        print("  ⚠️ 基本兼容，但建议进行测试验证")
        print("  📝 建议运行完整的功能测试")
    else:
        print("  ❌ 存在兼容性问题，需要更新代码")
        print("  🔧 建议检查缺失的函数和结构")

def main():
    """主函数"""
    print("local_ai_fixedV0.7.py 与专业知识库兼容性检查")
    print("="*50)
    
    # 加载数据
    knowledge_base = load_knowledge_base()
    config = load_config()
    
    if not knowledge_base or not config:
        print("❌ 无法加载必要文件，检查终止")
        return
    
    # 分析代码结构
    code_analysis = analyze_code_structure()
    if not code_analysis:
        print("❌ 无法分析代码文件，检查终止")
        return
    
    code_functions, code_content = code_analysis
    
    # 执行各项检查
    structure_ok = check_knowledge_base_structure(knowledge_base)
    compatibility_ok = check_code_compatibility(code_content, knowledge_base)
    rule_engine_ok = test_rule_engine_simulation(knowledge_base, config)
    knowledge_enhancement_ok = test_knowledge_enhancement_simulation(knowledge_base)
    
    # 生成报告
    compatibility_results = {
        "知识库结构检查": structure_ok,
        "代码兼容性检查": compatibility_ok,
        "规则引擎模拟": rule_engine_ok,
        "知识增强模拟": knowledge_enhancement_ok
    }
    
    generate_compatibility_report(knowledge_base, config, code_functions, compatibility_results)

if __name__ == "__main__":
    main()
