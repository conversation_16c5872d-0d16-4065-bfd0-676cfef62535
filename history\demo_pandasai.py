"""
PandasAI 本地大模型集成演示
使用pandasai_litellm方式
"""
import os
import sys
import pandas as pd
import matplotlib.pyplot as plt
import importlib.metadata
import warnings

# 检查PandasAI版本
try:
    pandasai_version = importlib.metadata.version('pandasai')
    print(f"检测到PandasAI版本: {pandasai_version}")
except importlib.metadata.PackageNotFoundError:
    pandasai_version = "unknown"
    warnings.warn("无法确定PandasAI版本，可能导致兼容性问题")

# 根据版本导入模块
try:
    if pandasai_version.startswith('3'):
        # PandasAI 3.x
        from pandasai_litellm import LiteLLM
        from pandasai import Agent, DataFrame, config
        print("使用PandasAI 3.x API")
    else:
        # PandasAI 2.x
        from pandasai_litellm import LiteLLM
        from pandasai import PandasAI as Agent
        from pandasai import SmartDataframe as DataFrame
        print("使用PandasAI 2.x API")
    
    PANDASAI_AVAILABLE = True
except ImportError as e:
    PANDASAI_AVAILABLE = False
    print(f"导入PandasAI模块失败: {str(e)}")
    print("请确保已安装pandasai和pandasai-litellm包")
    print("可以使用以下命令安装: pip install 'pandasai>=2.0' pandasai-litellm")
    sys.exit(1)

# 创建示例数据
data = {
    "Country": ["United States", "United Kingdom", "France", "Germany", "Italy", "Spain", "Canada", "Australia", "Japan", "China"],
    "GDP": [214332, 294514, 283368, 387443, 216456, 178256, 196863, 183218, 446823, 1478960],
    "Population": [334914, 68497, 68042, 83240, 60317, 47615, 38436, 25690, 125507, 1412175]
}

def demo_litellm():
    print("\n=== PandasAI LiteLLM演示 ===")
    
    # 创建DataFrame
    df = DataFrame(data)
    print(f"已创建PandasAI DataFrame，数据形状: {df.shape}")
    
    # 创建LiteLLM实例
    llm = LiteLLM(
        model="ollama/qwen2.5-coder:7b",  # 使用Ollama模型
        api_base="http://localhost:11434", # Ollama API地址
        temperature=0,                     # 设置温度为0，使结果更确定
        max_tokens=2000                    # 最大生成token数
    )
    print("已创建LiteLLM实例，temperature设置为0")
    
    # 根据版本处理查询
    query = "哪个国家的人均GDP最高？计算并列出前三名。"
    print(f"\n查询: {query}")
    
    try:
        if pandasai_version.startswith('3'):
            # PandasAI 3.x处理方式
            config.set({"llm": llm})
            print("已设置全局配置")
            result = df.chat(query)
        else:
            # PandasAI 2.x处理方式
            pandas_ai = Agent(llm)
            print("已创建PandasAI实例")
            result = pandas_ai.run(df, query)
            
        print(f"结果: {result}")
    except Exception as e:
        print(f"查询出错: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # 尝试生成可视化
    query_vis = "绘制一个柱状图，显示所有国家的人均GDP，并按降序排列。使用不同颜色，并添加标题和标签。"
    print(f"\n可视化查询: {query_vis}")
    try:
        if pandasai_version.startswith('3'):
            # PandasAI 3.x处理方式
            result = df.chat(query_vis)
        else:
            # PandasAI 2.x处理方式
            result = pandas_ai.run(df, query_vis)
            
        print("可视化已生成，请在界面中查看")
        
        # 如果在终端环境中，可以保存图表
        if hasattr(result, 'figure') or isinstance(result, plt.figure.Figure):
            fig = result.figure if hasattr(result, 'figure') else result
            fig.savefig("gdp_per_capita.png")
            print("图表已保存为 gdp_per_capita.png")
    except Exception as e:
        print(f"生成可视化时出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("PandasAI与本地大模型集成演示 (仅使用LiteLLM方式)")
    
    # 运行LiteLLM方式的演示
    demo_litellm()
    
    print("\n演示完成！") 