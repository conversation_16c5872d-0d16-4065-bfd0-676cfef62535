"""
使用pandasai和pandasai_litellm读取Excel文件并通过自然语言进行查询
"""
import os
import sys
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np
import tkinter as tk
from tkinter import ttk, filedialog, scrolledtext, messagebox
import matplotlib
import traceback
import requests
import json
import re
import io  # 用于处理字节流
from importlib import util  # 用于检查模块是否可导入

# 设置全局变量 - 配置 Ollama API URL
OLLAMA_API_URL = "http://localhost:11434"
OLLAMA_API_ENDPOINT = f"{OLLAMA_API_URL}/api"

# 设置matplotlib支持中文
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 用黑体显示中文
matplotlib.rcParams['axes.unicode_minus'] = False    # 正常显示负号

# 检查必要的依赖
try:
    import pandasai as pai
    from pandasai_litellm import LiteLLM
except ImportError:
    print("错误: 缺少必要的依赖。请安装 pandasai 和 pandasai_litellm:")
    print("pip install 'pandasai>=2.0' pandasai-litellm")
    sys.exit(1)

# 条件导入特定平台所需模块
if sys.platform == 'win32':
    # 检查是否已安装pywin32和PIL
    has_win32clipboard = util.find_spec("win32clipboard") is not None
    has_pil = util.find_spec("PIL") is not None
    
    if has_win32clipboard and has_pil:
        try:
            import win32clipboard
            from PIL import Image
        except ImportError:
            print("警告: 虽然检测到pywin32和PIL，但导入失败，部分图表复制功能可能不可用")

class OllamaAPI:
    """与Ollama API交互的类"""
    
    def __init__(self, base_url=OLLAMA_API_ENDPOINT):
        self.base_url = base_url
        
    def get_available_models(self):
        """获取可用模型列表"""
        tags_url = f"{self.base_url}/tags"
        try:
            response = requests.get(tags_url)
            if response.status_code == 200:
                result = response.json()
                if 'models' in result and isinstance(result['models'], list):
                    models = [model.get('name') for model in result['models'] if 'name' in model]
                    return models
                else:
                    return []
            else:
                return []
        except requests.exceptions.RequestException as e:
            print(f"获取模型列表失败: {str(e)}")
            return []
    
    def chat(self, model_name, messages):
        """与模型对话"""
        chat_url = f"{self.base_url}/chat"
        headers = {"Content-Type": "application/json"}
        
        payload = {
            "model": model_name,
            "messages": messages,
            "stream": False
        }
        
        try:
            response = requests.post(chat_url, headers=headers, data=json.dumps(payload))
            
            if response.status_code == 200:
                result = response.json()
                if 'message' in result and 'content' in result['message']:
                    return result['message']['content']
                elif 'choices' in result and len(result['choices']) > 0 and 'message' in result['choices'][0]:
                    return result['choices'][0]['message']['content']
                elif 'response' in result:
                    return result['response']
                else:
                    return f"响应格式未知: {json.dumps(result)}"
            else:
                return f"请求失败，状态码: {response.status_code}, 错误信息: {response.text}"
        
        except requests.exceptions.RequestException as e:
            return f"网络错误: {str(e)}"
        except Exception as e:
            return f"其他错误: {str(e)}"

class QueryPreprocessor:
    """
    查询预处理器 - 用于增强自然语言查询，改善与LLM的交互
    """
    
    # 查询模板映射
    QUERY_TEMPLATES = {
        # 计算统计值模板
        r'计算(.+?)的(平均值|总和|最大值|最小值|中位数|标准差)': "分析数据中'{0}'列的{1}。请返回准确的数值结果。",
        
        # 分组统计模板
        r'按(.+?)对(.+?)进行分组.*?(计算|统计)(.+?)': "根据'{0}'对数据进行分组，计算每组中'{1}'的统计值。请使用groupby和agg方法，并展示清晰的结果。",
        
        # 条件筛选模板
        r'筛选(.+?)大于|小于|等于|包含(.+?)的记录': "筛选出'{0}'{1}'{2}'的所有记录。请使用df.query或布尔索引方法，并返回完整的结果DataFrame。",
        
        # 删除数据模板
        r'删除(.+?)等于|大于|小于|包含(.+?)的数据': "删除满足条件的数据：'{0}'{1}'{2}'。请使用df.drop和适当的条件，返回处理后的DataFrame。",
        
        # 可视化模板
        r'(创建|绘制|画)(.+?)随(.+?)变化的(折线图|柱状图|散点图)': "创建一个{3}，展示'{1}'随'{2}'的变化。请确保图表有适当的标题和标签，使用plt或df.plot。",
        
        # 相关性分析模板
        r'分析(.+?)与(.+?)的(关系|相关性)': "分析'{0}'与'{1}'之间的相关性。请计算相关系数并考虑创建相关性热图或散点图。",
        
        # 排序模板
        r'按(.+?)(升序|降序)排列': "按'{0}'{1}排列数据。使用df.sort_values函数，ascending参数设为{'升序': True, '降序': False}['{1}']。",
    }
    
    @classmethod
    def enhance_query(cls, query, df):
        """
        增强自然语言查询，添加更多上下文和精确指令
        
        Args:
            query: 原始查询文本
            df: 数据DataFrame，用于提取列名等信息
        
        Returns:
            增强后的查询
        """
        # 复制原始查询
        enhanced_query = query
        
        # 尝试匹配查询模板并增强
        for pattern, template in cls.QUERY_TEMPLATES.items():
            match = re.search(pattern, query)
            if match:
                try:
                    # 根据模板和匹配组格式化查询
                    groups = match.groups()
                    enhanced_query = template.format(*groups)
                    break
                except Exception:
                    # 如果格式化失败，保持原样
                    pass
        
        # 添加数据上下文
        context = cls._generate_data_context(df)
        
        # 组合最终增强查询
        final_query = f"""任务: {enhanced_query}

数据上下文:
{context}

请将这个自然语言请求转换为正确的pandas代码并执行。确保结果准确并以用户友好的方式呈现。如果需要创建可视化，请确保图表清晰易读。
"""
        return final_query
    
    @staticmethod
    def _generate_data_context(df):
        """生成数据上下文信息"""
        if df is None:
            return "无可用数据"
            
        # 基本信息
        info = [
            f"数据形状: {df.shape[0]}行 x {df.shape[1]}列",
            f"列名: {', '.join(str(col) for col in df.columns)}",
        ]
        
        # 数据类型
        dtypes = []
        for col in df.columns:
            dtype = str(df[col].dtype)
            dtypes.append(f"'{col}': {dtype}")
        
        info.append(f"数据类型: {', '.join(dtypes)}")
        
        # 对于时间类型的列，特别标注
        date_cols = []
        for col in df.columns:
            if pd.api.types.is_datetime64_any_dtype(df[col]):
                date_cols.append(f"'{col}'")
        
        if date_cols:
            info.append(f"时间列: {', '.join(date_cols)}")
            
        # 如果有数值型列，添加一些基本统计信息
        num_cols = df.select_dtypes(include=[np.number]).columns
        if len(num_cols) > 0:
            info.append(f"数值列: {', '.join([f'{col}' for col in num_cols])}")
        
        return "\n".join(info)

class ExcelAIApp:
    """Excel AI应用主类"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("Excel AI助手")
        
        # 初始化API
        self.ollama_api = OllamaAPI()
        
        # 初始化变量
        self.model_name = tk.StringVar(value="llama3")
        self.temperature = tk.DoubleVar(value=0.0)
        self.max_tokens = tk.IntVar(value=2000)
        self.conversation_history = []
        self.current_excel_df = None
        self.current_excel_path = None
        self.current_figure = None
        self.canvas = None
        self.pai_df = None  # PandasAI DataFrame
        self.llm = None  # LLM实例
        
        # 多条件筛选相关变量
        self.multi_filter_frame = None
        self.filter_conditions = []
        self.logic_var = tk.StringVar(value="AND")
        
        # 文件修改状态跟踪
        self._original_excel_df = None
        self._file_modified = False
        
        # 创建界面
        self.create_widgets()
        self.load_available_models()
    
    def create_widgets(self):
        """创建GUI界面元素"""
        # 模型选择区域
        model_frame = ttk.LabelFrame(self.root, text="模型设置")
        model_frame.pack(padx=10, pady=5, fill="x")
        
        # 模型选择
        model_label = ttk.Label(model_frame, text="选择模型:")
        model_label.grid(row=0, column=0, padx=5, pady=5, sticky="w")
        
        self.model_combobox = ttk.Combobox(model_frame, textvariable=self.model_name, state="readonly")
        self.model_combobox.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        
        # 温度设置
        temp_label = ttk.Label(model_frame, text="Temperature:")
        temp_label.grid(row=1, column=0, padx=5, pady=5, sticky="w")
        
        temp_spinbox = ttk.Spinbox(model_frame, from_=0.0, to=1.0, increment=0.1, textvariable=self.temperature)
        temp_spinbox.grid(row=1, column=1, padx=5, pady=5, sticky="ew")
        
        # Max Tokens设置
        tokens_label = ttk.Label(model_frame, text="Max Tokens:")
        tokens_label.grid(row=2, column=0, padx=5, pady=5, sticky="w")
        
        tokens_spinbox = ttk.Spinbox(model_frame, from_=500, to=4000, increment=100, textvariable=self.max_tokens)
        tokens_spinbox.grid(row=2, column=1, padx=5, pady=5, sticky="ew")
        
        # 设置列权重
        model_frame.columnconfigure(1, weight=1)
        
        # 对话区域
        chat_frame = ttk.LabelFrame(self.root, text="对话")
        chat_frame.pack(padx=10, pady=5, expand=True, fill="both")
        
        self.chat_display = scrolledtext.ScrolledText(chat_frame, wrap=tk.WORD, state="disabled")
        self.chat_display.pack(padx=5, pady=5, expand=True, fill="both")
        
        # 输入区域
        input_frame = ttk.Frame(self.root)
        input_frame.pack(padx=10, pady=5, fill="x")
        
        self.user_input = tk.Text(input_frame, height=4, wrap=tk.WORD)
        self.user_input.pack(padx=5, pady=5, fill="x")
        self.user_input.bind("<Return>", lambda event: self.send_message())
        
        # 按钮区域
        button_frame = ttk.Frame(input_frame)
        button_frame.pack(fill="x")
        
        load_excel_btn = ttk.Button(button_frame, text="加载Excel", command=self.load_excel)
        load_excel_btn.pack(side="left", padx=5, pady=5)
        
        send_btn = ttk.Button(button_frame, text="发送", command=self.send_message)
        send_btn.pack(side="left", padx=5, pady=5)

        translate_btn = ttk.Button(button_frame, text="翻译", command=self.translate_text)
        translate_btn.pack(side="left", padx=5, pady=5)
        
        # 添加特殊操作按钮
        special_ops_btn = ttk.Button(button_frame, text="特殊操作", command=self.show_special_operations)
        special_ops_btn.pack(side="left", padx=5, pady=5)
        
        clear_btn = ttk.Button(button_frame, text="清空记录", command=self.clear_history)
        clear_btn.pack(side="right", padx=5, pady=5)
        
        save_btn = ttk.Button(button_frame, text="保存历史", command=self.save_history)
        save_btn.pack(side="right", padx=5, pady=5)
        
        # 可视化区域（初始隐藏）
        self.viz_frame = ttk.LabelFrame(self.root, text="数据可视化")
        # 不立即显示，等待有数据时再显示
        
        # 结果操作区域（初始隐藏）
        self.result_frame = ttk.Frame(self.root)
        # 不立即显示，等待有结果时再显示
        
        # 特殊操作弹窗（初始不创建）
        self.special_ops_window = None
    
    def load_available_models(self):
        """加载可用的Ollama模型列表"""
        try:
            models = self.ollama_api.get_available_models()
            
            if models:
                self.model_combobox["values"] = models
                self.model_name.set(models[0] if "llama3" not in models else "llama3")
                self.update_chat("已加载可用模型列表")
                return
        except Exception as e:
            pass
            
        # 如果无法获取模型列表，使用默认值
        default_models = ["llama3", "qwen2.5-coder", "mistral", "gemma2"]
        self.model_combobox["values"] = default_models
        self.update_chat("无法连接到Ollama服务，使用默认模型列表")
    
    def load_excel(self):
        """加载Excel文件并转换为PandasAI DataFrame"""
        # 选择Excel文件
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls")]
        )
        if not file_path:
            return
            
        self.update_chat(f"已选择Excel文件: {file_path}")
        self.current_excel_path = file_path
        
        try:
            # 读取Excel文件为pandas DataFrame
            self.current_excel_df = pd.read_excel(file_path)
            
            # 创建原始数据的备份
            self._original_excel_df = self.current_excel_df.copy()
            self._file_modified = False  # 添加文件修改状态标志
            
            # 显示基本信息
            self.update_chat(f"Excel文件已加载，共{self.current_excel_df.shape[0]}行，{self.current_excel_df.shape[1]}列")
            self.update_chat(f"列名: {', '.join(str(col) for col in self.current_excel_df.columns)}")
            
            # 显示前5行数据预览
            preview = self.current_excel_df.head(5).to_string()
            self.update_chat(f"数据预览:\n{preview}")
            
            # 初始化LLM
            self.initialize_llm()
            
            # 转换为PandasAI DataFrame
            self.pai_df = pai.DataFrame(self.current_excel_df)
            self.update_chat("数据已加载AI引擎，可以使用自然语言进行查询处理，仅推荐试用")
            
            # 提示用户输入查询
            self.update_chat("\n可使用自然语言查询Excel数据，例如:")
            
            # 获取列名
            columns = list(self.current_excel_df.columns)
            numeric_cols = self.current_excel_df.select_dtypes(include=[np.number]).columns
            text_cols = self.current_excel_df.select_dtypes(include=['object']).columns
            date_cols = []
            
            # 检测日期列
            for col in columns:
                if pd.api.types.is_datetime64_any_dtype(self.current_excel_df[col]):
                    date_cols.append(col)
            
            # 生成与数据相关的示例
            examples = []
            
            # 统计示例
            if len(numeric_cols) > 0:
                col = numeric_cols[0]
                examples.append(f"- 计算{col}的平均值和总和")
            
            # 分组示例
            if len(numeric_cols) > 0 and len(text_cols) > 0:
                group_col = text_cols[0]
                value_col = numeric_cols[0]
                examples.append(f"- 按{group_col}对{value_col}进行分组并计算统计数据")
            
            # 时间序列示例
            if len(date_cols) > 0 and len(numeric_cols) > 0:
                date_col = date_cols[0]
                value_col = numeric_cols[0]
                examples.append(f"- 创建{value_col}随{date_col}变化的折线图")
            
            # 筛选示例
            if len(columns) > 0:
                col = columns[0]
                examples.append(f"- 筛选出{col}大于特定值的记录")
            
            # 删除示例
            if len(columns) > 0:
                col = columns[0]
                examples.append(f"- 删除{col}等于特定值的数据")
                
            # 相关性分析
            if len(numeric_cols) >= 2:
                col1 = numeric_cols[0]
                col2 = numeric_cols[1]
                examples.append(f"- 分析{col1}与{col2}的相关性")
                
            # 排序示例
            if len(columns) > 0:
                col = columns[0]
                examples.append(f"- 按{col}降序排列数据")
                
            # 数据转换示例
            if len(columns) > 0:
                col = columns[0]
                examples.append(f"- 将{col}转换为另一种数据类型")
                
            # 缺失值处理
            examples.append("- 检查并处理数据中的缺失值")
            
            # 数据透视表示例
            if len(text_cols) > 0 and len(numeric_cols) > 0:
                index_col = text_cols[0]
                value_col = numeric_cols[0]
                examples.append(f"- 创建以{index_col}为索引的数据透视表，计算{value_col}的统计值")
            
            # 显示所有示例
            for example in examples:
                self.update_chat(example)
            
            # 特殊操作提示
            self.update_chat("\n也可以点击'特殊操作'按钮进行更精确的数据处理")
            
        except Exception as e:
            self.update_chat(f"加载Excel文件失败: {str(e)}")
            traceback.print_exc()
    
    def initialize_llm(self):
        """初始化LLM"""
        try:
            # 创建更详细的系统提示
            system_prompt = """你是一位专业的数据分析助手，专精于pandas数据分析与可视化。
你的任务是将自然语言转化为精确的pandas代码，并执行分析任务。
请专注于以下能力：
1. 精确计算列的统计数据（平均值、总和、最大值、最小值等）
2. 按指定列对数据进行分组和聚合
3. 创建各种类型的可视化图表（折线图、柱状图、饼图等）
4. 根据条件筛选、排序和删除数据
5. 转换和清洗数据
6. 分析数据中的趋势和关系

始终返回清晰的结果，对于复杂分析，提供必要的解释。"""

            # 为pandas操作提供上下文示例
            additional_context = """
- 计算平均值和总和: df['列名'].mean(), df['列名'].sum()
- 分组统计: df.groupby('分组列')['值列'].agg(['mean', 'sum'])
- 数据筛选: df[df['列名'] > 值], df.query('条件表达式')
- 数据删除: df.drop(df[df['列名'] == 值].index)
- 时间序列图: df.plot(x='时间列', y='值列', kind='line')
- 条件替换: df.loc[df['列名'] == 值, '目标列'] = 新值
"""

            # 创建LiteLLM实例
            self.llm = LiteLLM(
                model=f"ollama/{self.model_name.get()}",  # 使用ollama/模型名称格式
                api_base=OLLAMA_API_URL,  # 使用全局变量作为 Ollama API 地址
                temperature=self.temperature.get(),  # 温度参数
                max_tokens=self.max_tokens.get(),  # 最大token数
                system_prompt=system_prompt,  # 添加系统提示
                additional_context=additional_context,  # 添加上下文示例
                enable_cache=True  # 启用缓存加速重复查询
            )
            
            # 设置pandasai配置
            pai.config.set({
                "llm": self.llm,
                "enable_cache": True,  # 启用缓存
                "use_error_correction_framework": True,  # 启用错误修正框架
                "save_charts": True,  # 保存图表
                "verbose": True,  # 启用详细日志
                "enforce_privacy": False,  # 关闭隐私保护以提高本地性能
                "open_charts": False  # 不自动打开图表
            })
            
            self.update_chat(f"已初始化LLM: {self.model_name.get()}, temperature={self.temperature.get()}")
            return True
        except Exception as e:
            self.update_chat(f"初始化LLM失败: {str(e)}")
            traceback.print_exc()
            return False
    
    def send_message(self, event=None):
        """发送用户查询或普通对话"""
        query = self.user_input.get("1.0", tk.END).strip()
        if not query:
            return
            
        self.user_input.delete("1.0", tk.END)
        self.update_chat(f"您: {query}")
        
        # 检查是否已加载数据，如果已加载则执行Excel查询，否则进行普通对话
        if self.pai_df is not None:
            # 处理Excel查询
            self.process_query(query)
        else:
            # 普通对话
            self.normal_chat(query)
    
    def normal_chat(self, message):
        """与模型进行普通对话"""
        try:
            # 添加到对话历史
            self.conversation_history.append({"role": "user", "content": message})
            
            # 调用模型进行对话
            response = self.ollama_api.chat(self.model_name.get(), self.conversation_history)
            
            # 显示模型回复
            self.update_chat(f"模型: {response}")
            
            # 添加回复到对话历史
            self.conversation_history.append({"role": "assistant", "content": response})
            
        except Exception as e:
            self.update_chat(f"对话出错: {str(e)}")
            traceback.print_exc()
    
    def translate_text(self):
        """翻译对话框中的文本"""
        # 获取用户输入
        text = self.user_input.get("1.0", tk.END).strip()
        
        if not text:
            self.update_chat("请输入需要翻译的内容")
            return
            
        # 判断文本语言
        is_chinese = any('\u4e00' <= char <= '\u9fff' for char in text)
        
        # 设置翻译提示
        if is_chinese:
            # 中文翻译成英文
            system_prompt = "你是翻译引擎。你的唯一任务是将中文文本翻译成英文。只输出翻译后的文本，不要添加任何解释、标点符号修改或额外内容。"
            self.update_chat("正在将中文翻译为英文...")
        else:
            # 英文翻译成中文
            system_prompt = "你是翻译引擎。你的唯一任务是将英文文本翻译成中文。只输出翻译后的文本，不要添加任何解释、标点符号修改或额外内容。"
            self.update_chat("正在将英文翻译为中文...")
        
        # 调用模型进行翻译
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": text}
        ]
        
        try:
            # 获取翻译结果
            translation = self.ollama_api.chat(self.model_name.get(), messages)
            
            # 清理翻译结果 - 移除可能的"翻译结果："等前缀
            if ":" in translation:
                parts = translation.split(":", 1)
                if len(parts) > 1 and len(parts[0]) < 20:  # 确保前缀不太长
                    translation = parts[1].strip()
            
            # 显示翻译结果
            self.update_chat(f"翻译结果: {translation}")
            
            # 将翻译结果放入输入框
            self.user_input.delete("1.0", tk.END)
            self.user_input.insert(tk.END, translation)
        except Exception as e:
            self.update_chat(f"翻译出错: {str(e)}")
            traceback.print_exc()
    
    def process_query(self, query):
        """处理自然语言查询"""
        try:
            self.update_chat("正在处理查询...")
            
            # 增强查询
            enhanced_query = QueryPreprocessor.enhance_query(query, self.current_excel_df)
            
            # 最大重试次数
            max_retries = 2
            retries = 0
            last_error = None
            
            while retries <= max_retries:
                try:
                    if retries > 0:
                        self.update_chat(f"尝试重新处理查询 (尝试 {retries}/{max_retries})...")
                        
                        # 第二次尝试时，添加更具体的指导
                        if retries == 1:
                            enhanced_query = self._add_retry_guidance(enhanced_query, last_error)
                    
                    # 使用PandasAI的DataFrame.chat方法处理查询
                    result = self.pai_df.chat(enhanced_query)
                    
                    # 检查结果是否有效
                    if result is None or (isinstance(result, str) and "错误" in result):
                        raise Exception(f"无效结果: {result}")
                    
                    # 处理结果
                    self.handle_result(result)
                    return  # 成功，退出
                    
                except Exception as e:
                    last_error = str(e)
                    retries += 1
                    
                    if retries > max_retries:
                        raise  # 达到最大重试次数，抛出异常
                    
                    # 修改下一次尝试的温度
                    current_temp = self.temperature.get()
                    new_temp = max(0, current_temp - 0.1)  # 降低温度增加确定性
                    self.temperature.set(new_temp)
                    
                    # 重新初始化LLM
                    self.initialize_llm()
                    
                    self.update_chat(f"查询处理失败: {str(e)}")
                    self.update_chat(f"正在调整参数并重试 (temperature={new_temp})...")
            
        except Exception as e:
            self.update_chat(f"处理查询最终失败: {str(e)}")
            traceback.print_exc()
    
    def _add_retry_guidance(self, query, error_msg):
        """根据错误添加更具体的指导"""
        # 提取常见错误关键词
        error_types = {
            "列名": ["找不到列", "不存在", "not exist", "KeyError", "未找到"],
            "语法错误": ["SyntaxError", "语法错误", "syntax", "invalid syntax"],
            "类型错误": ["TypeError", "类型错误", "cannot convert", "not supported"],
            "值错误": ["ValueError", "值错误", "could not convert", "invalid literal"],
            "索引错误": ["IndexError", "索引错误", "out of bounds", "超出范围"]
        }
        
        error_type = "未知错误"
        for err_type, keywords in error_types.items():
            if any(kw in error_msg for kw in keywords):
                error_type = err_type
                break
        
        # 根据错误类型添加具体指导
        guidance = {
            "列名": "请注意检查列名是否正确，确保使用DataFrame中实际存在的列。",
            "语法错误": "请修复pandas代码中的语法错误，确保所有括号和引号正确匹配。",
            "类型错误": "请确保操作的数据类型正确，可能需要进行类型转换。",
            "值错误": "请检查数据值是否有效，可能需要处理缺失值或非法值。",
            "索引错误": "请确保索引在有效范围内，检查DataFrame的实际大小。",
            "未知错误": "请检查代码逻辑，尝试使用更简单的pandas操作。"
        }
        
        # 为错误类型添加示例代码
        examples = {
            "列名": """
# 检查列名是否正确
print(df.columns)
# 使用正确的列名
result = df['正确的列名'].mean()
""",
            "语法错误": """
# 正确的语法示例
result = df.groupby('分组列')['值列'].agg(['mean', 'sum'])
# 条件筛选
filtered_df = df[df['列名'] > 值]
""",
            "类型错误": """
# 类型转换示例
df['数值列'] = pd.to_numeric(df['数值列'], errors='coerce')
df['日期列'] = pd.to_datetime(df['日期列'], errors='coerce')
""",
            "值错误": """
# 处理缺失值
df = df.dropna(subset=['列名'])
# 或填充缺失值
df['列名'] = df['列名'].fillna(0)
""",
            "索引错误": """
# 安全访问索引
if len(df) > 0:
    result = df.iloc[0:min(10, len(df))]
""",
            "未知错误": """
# 简化操作
# 1. 先筛选
filtered_df = df[条件]
# 2. 再聚合或计算
result = filtered_df.操作()
"""
        }
        
        # 添加指导和示例到查询
        retry_guidance = f"""
遇到错误: {error_msg}
错误类型: {error_type}
解决建议: {guidance[error_type]}

参考代码示例:
{examples[error_type]}

请修正错误并重新尝试以下任务:
"""
        
        # 结合原始查询和指导
        enhanced_query = retry_guidance + query
        
        return enhanced_query
    
    def handle_result(self, result):
        """处理查询结果"""
        if result is None:
            self.update_chat("查询结果为空")
            return
            
        # 根据结果类型处理
        if isinstance(result, pd.DataFrame):
            # DataFrame结果
            if len(result) > 10:
                preview = result.head(10).to_string()
                self.update_chat(f"查询结果(显示前10行):\n{preview}")
                self.update_chat(f"总行数: {len(result)}")
            else:
                self.update_chat(f"查询结果:\n{result.to_string()}")
            
            # 更新当前DataFrame为结果
            self.current_excel_df = result
            self.pai_df = pai.DataFrame(result)
            
            # 设置文件修改标志
            self._file_modified = True
            
            # 显示保存结果按钮
            self.show_result_buttons()
            
        elif isinstance(result, str):
            # 字符串结果
            self.update_chat(f"查询结果: {result}")
            
        elif hasattr(result, 'figure'):
            # 图表结果(带figure属性)
            self.update_chat("生成了可视化结果")
            self.display_visualization(result.figure)
            
        elif isinstance(result, plt.Figure):
            # 直接的Figure实例
            self.update_chat("生成了可视化结果")
            self.display_visualization(result)
            
        else:
            # 其他类型结果
            self.update_chat(f"查询结果: {str(result)}")
    
    def display_visualization(self, fig):
        """在GUI中显示可视化图表"""
        try:
            # 清除之前的可视化
            if self.canvas:
                self.canvas.get_tk_widget().destroy()
            
            # 显示可视化区域
            if not self.viz_frame.winfo_ismapped():
                self.viz_frame.pack(padx=10, pady=5, fill="both", expand=True)
            
            # 创建新的画布
            self.canvas = FigureCanvasTkAgg(fig, master=self.viz_frame)
            self.canvas.draw()
            self.canvas.get_tk_widget().pack(fill="both", expand=True)
            
            # 保存当前图表
            self.current_figure = fig
            
            # 显示保存图表按钮
            self.show_viz_buttons()
            
        except Exception as e:
            self.update_chat(f"显示图表时出错: {str(e)}")
            traceback.print_exc()
    
    def show_viz_buttons(self):
        """显示可视化操作按钮"""
        # 检查按钮区域是否已存在
        viz_button_frame = ttk.Frame(self.viz_frame)
        viz_button_frame.pack(fill="x", padx=5, pady=5)
        
        # 保存图表按钮
        save_chart_btn = ttk.Button(
            viz_button_frame, 
            text="保存图表", 
            command=self.save_chart
        )
        save_chart_btn.pack(side="left", padx=5, pady=5)
        
        # 调整图表大小按钮
        resize_chart_btn = ttk.Button(
            viz_button_frame, 
            text="调整图表", 
            command=self.resize_chart
        )
        resize_chart_btn.pack(side="left", padx=5, pady=5)
        
        # 复制图表到剪贴板按钮
        copy_chart_btn = ttk.Button(
            viz_button_frame, 
            text="复制图表", 
            command=self.copy_chart_to_clipboard
        )
        copy_chart_btn.pack(side="left", padx=5, pady=5)
        
        # 关闭图表按钮
        close_chart_btn = ttk.Button(
            viz_button_frame, 
            text="关闭图表", 
            command=self.close_visualization
        )
        close_chart_btn.pack(side="right", padx=5, pady=5)
    
    def close_visualization(self):
        """关闭可视化区域"""
        if self.viz_frame.winfo_ismapped():
            self.viz_frame.pack_forget()
        
        # 清除当前图表
        self.current_figure = None
        
        # 清除画布
        if self.canvas:
            self.canvas.get_tk_widget().destroy()
            self.canvas = None
    
    def resize_chart(self):
        """调整图表大小"""
        if self.current_figure is None:
            self.update_chat("没有可调整的图表")
            return
            
        # 创建调整窗口
        resize_window = tk.Toplevel(self.root)
        resize_window.title("调整图表大小")
        resize_window.geometry("300x200")
        resize_window.resizable(False, False)
        
        # 图表宽度
        width_frame = ttk.Frame(resize_window)
        width_frame.pack(fill="x", padx=10, pady=5)
        
        width_label = ttk.Label(width_frame, text="宽度(英寸):")
        width_label.pack(side="left", padx=5)
        
        width_var = tk.DoubleVar(value=self.current_figure.get_figwidth())
        width_spinbox = ttk.Spinbox(width_frame, from_=1, to=20, increment=0.5, textvariable=width_var, width=10)
        width_spinbox.pack(side="left", padx=5)
        
        # 图表高度
        height_frame = ttk.Frame(resize_window)
        height_frame.pack(fill="x", padx=10, pady=5)
        
        height_label = ttk.Label(height_frame, text="高度(英寸):")
        height_label.pack(side="left", padx=5)
        
        height_var = tk.DoubleVar(value=self.current_figure.get_figheight())
        height_spinbox = ttk.Spinbox(height_frame, from_=1, to=20, increment=0.5, textvariable=height_var, width=10)
        height_spinbox.pack(side="left", padx=5)
        
        # DPI设置
        dpi_frame = ttk.Frame(resize_window)
        dpi_frame.pack(fill="x", padx=10, pady=5)
        
        dpi_label = ttk.Label(dpi_frame, text="DPI:")
        dpi_label.pack(side="left", padx=5)
        
        dpi_var = tk.IntVar(value=self.current_figure.dpi)
        dpi_spinbox = ttk.Spinbox(dpi_frame, from_=72, to=600, increment=10, textvariable=dpi_var, width=10)
        dpi_spinbox.pack(side="left", padx=5)
        
        # 应用按钮
        button_frame = ttk.Frame(resize_window)
        button_frame.pack(fill="x", padx=10, pady=10)
        
        apply_btn = ttk.Button(
            button_frame, 
            text="应用", 
            command=lambda: self._apply_chart_resize(
                width_var.get(), 
                height_var.get(), 
                dpi_var.get(),
                resize_window
            )
        )
        apply_btn.pack(side="left", padx=5)
        
        cancel_btn = ttk.Button(
            button_frame,
            text="取消",
            command=resize_window.destroy
        )
        cancel_btn.pack(side="right", padx=5)
    
    def _apply_chart_resize(self, width, height, dpi, window):
        """应用图表大小调整"""
        try:
            # 调整图表大小
            self.current_figure.set_size_inches(width, height)
            self.current_figure.set_dpi(dpi)
            
            # 重绘画布
            self.canvas.draw()
            
            self.update_chat(f"已调整图表大小为 {width}x{height} 英寸，DPI={dpi}")
            
            # 关闭调整窗口
            window.destroy()
        except Exception as e:
            self.update_chat(f"调整图表大小失败: {str(e)}")
            traceback.print_exc()
    
    def copy_chart_to_clipboard(self):
        """复制图表到剪贴板"""
        if self.current_figure is None:
            self.update_chat("没有可复制的图表")
            return
            
        try:
            # 保存为临时文件
            temp_file = os.path.join(os.environ.get('TEMP', '.'), 'temp_chart.png')
            self.current_figure.savefig(temp_file, dpi=300, bbox_inches='tight')
            
            # 复制到剪贴板
            if sys.platform == 'win32':
                # Windows平台使用PIL和win32clipboard
                try:
                    import win32clipboard
                    
                    image = Image.open(temp_file)
                    
                    # 复制到剪贴板
                    output = io.BytesIO()
                    image.convert('RGB').save(output, 'BMP')
                    data = output.getvalue()[14:]  # 移除BMP文件头
                    output.close()
                    
                    win32clipboard.OpenClipboard()
                    win32clipboard.EmptyClipboard()
                    win32clipboard.SetClipboardData(win32clipboard.CF_DIB, data)
                    win32clipboard.CloseClipboard()
                    
                    self.update_chat("已复制图表到剪贴板")
                except ImportError:
                    self.update_chat("无法复制图表：需要安装PIL和pywin32库")
                except Exception as e:
                    self.update_chat(f"复制图表失败: {str(e)}")
            else:
                self.update_chat("当前平台不支持复制图表到剪贴板")
                
            # 清理临时文件
            try:
                os.remove(temp_file)
            except:
                pass
                
        except Exception as e:
            self.update_chat(f"复制图表失败: {str(e)}")
            traceback.print_exc()
    
    def save_chart(self):
        """保存图表"""
        if self.current_figure is None:
            self.update_chat("没有可保存的图表")
            return
            
        file_path = filedialog.asksaveasfilename(
            defaultextension=".png",
            filetypes=[
                ("PNG图片", "*.png"), 
                ("JPEG图片", "*.jpg"), 
                ("PDF文件", "*.pdf"), 
                ("SVG矢量图", "*.svg"),
                ("EPS文件", "*.eps")
            ],
            initialfile="数据可视化.png"
        )
        if not file_path:
            return
            
        try:
            # 创建保存选项窗口
            save_options = tk.Toplevel(self.root)
            save_options.title("保存选项")
            save_options.geometry("300x200")
            save_options.resizable(False, False)
            
            # DPI设置
            dpi_frame = ttk.Frame(save_options)
            dpi_frame.pack(fill="x", padx=10, pady=5)
            
            dpi_label = ttk.Label(dpi_frame, text="DPI:")
            dpi_label.pack(side="left", padx=5)
            
            dpi_var = tk.IntVar(value=300)
            dpi_spinbox = ttk.Spinbox(dpi_frame, from_=72, to=600, increment=10, textvariable=dpi_var, width=10)
            dpi_spinbox.pack(side="left", padx=5)
            
            # 透明背景
            transparent_var = tk.BooleanVar(value=False)
            transparent_check = ttk.Checkbutton(
                save_options, 
                text="透明背景 (PNG和SVG格式)", 
                variable=transparent_var
            )
            transparent_check.pack(padx=10, pady=5, anchor="w")
            
            # 紧凑布局
            tight_var = tk.BooleanVar(value=True)
            tight_check = ttk.Checkbutton(
                save_options, 
                text="紧凑布局 (无额外边距)", 
                variable=tight_var
            )
            tight_check.pack(padx=10, pady=5, anchor="w")
            
            # 按钮
            button_frame = ttk.Frame(save_options)
            button_frame.pack(fill="x", padx=10, pady=10)
            
            save_btn = ttk.Button(
                button_frame, 
                text="保存", 
                command=lambda: self._save_chart_with_options(
                    file_path,
                    dpi_var.get(),
                    transparent_var.get(),
                    tight_var.get(),
                    save_options
                )
            )
            save_btn.pack(side="left", padx=5)
            
            cancel_btn = ttk.Button(
                button_frame,
                text="取消",
                command=save_options.destroy
            )
            cancel_btn.pack(side="right", padx=5)
            
        except Exception as e:
            self.update_chat(f"保存图表失败: {str(e)}")
            traceback.print_exc()
    
    def _save_chart_with_options(self, file_path, dpi, transparent, tight, window):
        """使用指定选项保存图表"""
        try:
            # 准备保存选项
            save_options = {
                'dpi': dpi,
                'transparent': transparent
            }
            
            if tight:
                save_options['bbox_inches'] = 'tight'
            
            # 保存图表
            self.current_figure.savefig(file_path, **save_options)
            
            # 显示完整保存路径
            abs_path = os.path.abspath(file_path)
            self.update_chat(f"已保存图表到: {abs_path}")
            
            # 显示图表详细信息
            file_size = os.path.getsize(abs_path) / 1024  # KB
            self.update_chat(f"图表大小: {file_size:.1f} KB, 分辨率: {dpi} DPI")
            
            # 在Windows系统中打开文件所在文件夹
            if sys.platform == 'win32':
                os.system(f'explorer /select,"{abs_path}"')
            
            # 关闭选项窗口
            window.destroy()
        except Exception as e:
            self.update_chat(f"保存图表失败: {str(e)}")
            traceback.print_exc()
    
    def show_result_buttons(self):
        """显示结果操作按钮"""
        # 检查按钮是否已存在
        if not self.result_frame.winfo_ismapped():
            self.result_frame.pack(padx=10, pady=5, fill="x")
            
            # 清空之前的按钮
            for widget in self.result_frame.winfo_children():
                widget.destroy()
            
            # 添加保存Excel按钮
            save_excel_btn = ttk.Button(
                self.result_frame, 
                text="保存Excel结果", 
                command=self.save_excel_result
            )
            save_excel_btn.pack(side="left", padx=5, pady=5)
            
            # 添加保存为新文件按钮
            save_as_new_btn = ttk.Button(
                self.result_frame, 
                text="另存为新文件", 
                command=lambda: self.save_excel_result(as_new=True)
            )
            save_as_new_btn.pack(side="left", padx=5, pady=5)
            
            # 如果有图表，添加保存图表按钮
            if self.current_figure is not None:
                save_chart_btn = ttk.Button(
                    self.result_frame, 
                    text="保存图表", 
                    command=self.save_chart
                )
                save_chart_btn.pack(side="left", padx=5, pady=5)
    
    def save_excel_result(self, as_new=False):
        """保存Excel结果"""
        if self.current_excel_df is None:
            self.update_chat("没有可保存的数据")
            return
            
        # 检查是否为保存修改到原文件
        is_saving_to_original = False
        if not as_new and self.current_excel_path and self._file_modified:
            is_saving_to_original = True
            
            # 告知用户将修改原文件
            if not self._ask_yes_no(f"确定要将修改保存到原始文件?\n{self.current_excel_path}\n\n建议使用'另存为新文件'以保留原始数据。"):
                # 自动切换为另存为新文件
                as_new = True
                self.update_chat("已切换为另存为新文件模式")
        
        # 默认文件名和标题
        default_filename = "查询结果.xlsx"
        dialog_title = "保存查询结果"
        
        # 如果是保存为新文件，使用不同的文件名和标题
        if as_new:
            # 从原始文件名生成新文件名
            if self.current_excel_path:
                basename = os.path.basename(self.current_excel_path)
                name, ext = os.path.splitext(basename)
                default_filename = f"{name}_processed{ext}"
            else:
                default_filename = "处理结果.xlsx"
            dialog_title = "另存为新文件"
                
        file_path = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            filetypes=[("Excel文件", "*.xlsx"), ("CSV文件", "*.csv")],
            initialfile=default_filename,
            title=dialog_title
        )
        if not file_path:
            return
            
        try:
            # 根据文件扩展名决定保存方式
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext == '.csv':
                self.current_excel_df.to_csv(file_path, index=False, encoding='utf-8-sig')
            else:
                # 检查是否需要保存数据类型和格式
                if self._ask_yes_no("是否保留数据格式（例如日期格式、数字格式等）？"):
                    # 创建ExcelWriter以添加格式
                    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                        self.current_excel_df.to_excel(writer, index=False, sheet_name='数据结果')
                        workbook = writer.book
                        worksheet = writer.sheets['数据结果']
                        
                        # 自动调整列宽
                        for i, col in enumerate(self.current_excel_df.columns):
                            max_length = max(
                                self.current_excel_df[col].astype(str).map(len).max(),
                                len(str(col))
                            )
                            # 设置列宽，加一点额外宽度
                            worksheet.column_dimensions[chr(65 + i)].width = max_length + 4
                else:
                    self.current_excel_df.to_excel(file_path, index=False)
            
            # 显示完整保存路径
            abs_path = os.path.abspath(file_path)
            self.update_chat(f"已保存数据到: {abs_path}")
            
            # 在Windows系统中打开文件所在文件夹
            if sys.platform == 'win32':
                os.system(f'explorer /select,"{abs_path}"')
                
            # 如果是保存为新文件，询问是否切换到新文件
            if as_new:
                if self._ask_yes_no("是否切换到新保存的文件？"):
                    self.current_excel_path = file_path
                    # 重新加载新文件
                    if file_ext == '.xlsx':
                        self.current_excel_df = pd.read_excel(file_path)
                    else:
                        self.current_excel_df = pd.read_csv(file_path, encoding='utf-8-sig')
                    self.pai_df = pai.DataFrame(self.current_excel_df)
                    
                    # 重置修改状态
                    self._original_excel_df = self.current_excel_df.copy()
                    self._file_modified = False
                    
                    self.update_chat(f"当前工作文件已切换到: {os.path.basename(file_path)}")
            elif is_saving_to_original:
                # 已保存到原始文件，重置修改状态
                self._original_excel_df = self.current_excel_df.copy()
                self._file_modified = False
                self.update_chat("已更新原始文件")
                
        except Exception as e:
            self.update_chat(f"保存Excel文件失败: {str(e)}")
            traceback.print_exc()
    
    def _ask_yes_no(self, question):
        """显示是/否对话框"""
        return tk.messagebox.askyesno("确认", question)
    
    def save_history(self):
        """保存对话历史"""
        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt")],
            initialfile="对话历史.txt"
        )
        if not file_path:
            return
            
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                # 获取所有文本
                self.chat_display.config(state="normal")
                text = self.chat_display.get("1.0", tk.END)
                self.chat_display.config(state="disabled")
                
                f.write(text)
            self.update_chat(f"已保存对话历史到: {file_path}")
        except Exception as e:
            self.update_chat(f"保存文件失败: {str(e)}")
            traceback.print_exc()
    
    def clear_history(self):
        """清空聊天记录和上下文"""
        # 清空聊天显示
        self.chat_display.config(state="normal")
        self.chat_display.delete("1.0", tk.END)
        self.chat_display.config(state="disabled")
        
        # 重置对话历史
        self.conversation_history = []
        
        # 重新初始化LLM
        if self.current_excel_df is not None:
            self.initialize_llm()
            self.update_chat("已重新初始化LLM")
        
        self.update_chat("对话历史已清空")
    
    def update_chat(self, message):
        """更新聊天显示"""
        self.chat_display.config(state="normal")
        self.chat_display.insert(tk.END, message + "\n")
        self.chat_display.config(state="disabled")
        self.chat_display.see(tk.END)

    def show_special_operations(self):
        """显示特殊操作弹窗"""
        if self.current_excel_df is None:
            self.update_chat("请先加载Excel文件")
            return
        
        # 如果窗口已存在，先销毁
        if self.special_ops_window is not None and self.special_ops_window.winfo_exists():
            self.special_ops_window.destroy()
        
        # 重置多条件筛选相关变量
        self.multi_filter_frame = None
        self.filter_conditions = []
        self.logic_var.set("AND")
        
        # 创建新窗口
        self.special_ops_window = tk.Toplevel(self.root)
        self.special_ops_window.title("特殊数据操作")
        self.special_ops_window.geometry("700x600")  # 增加窗口大小
        self.special_ops_window.resizable(True, True)  # 允许调整窗口大小
        
        # 设置窗口图标和样式
        if sys.platform == 'win32':
            self.special_ops_window.iconbitmap(default='')  # 使用默认图标
            
        # 操作类型描述映射
        op_descriptions = {
            "delete_rows": "删除满足指定条件的数据行",
            "replace_values": "替换满足条件的值为新值",
            "convert_type": "将指定列的数据类型转换为目标类型",
            "handle_missing": "处理指定列中的缺失值",
            "create_column": "基于表达式创建新列",
            "pivot_table": "创建数据透视表进行汇总分析",
            "sort_data": "按指定列对数据进行排序",
            "normalize_data": "对数值列进行归一化或标准化处理",
            "multi_filter": "使用多个条件组合筛选数据"  # 添加多条件筛选的描述
        }
        
        # 表达式提示映射
        expr_tips = {
            "计算": "示例: column1 + column2 * 2\n可以使用数学运算符 +, -, *, /, 函数如 round(), abs()\n列名直接使用，不需要引号",
            "条件": "示例: column1 > 10 或 column1 == 'value'\n可以使用比较运算符 >, <, ==, !=, >=, <=\n列名直接使用，文本值需要引号",
            "连接": "示例: column1 + '-' + column2\n使用+连接列和字符串，字符串需要用引号"
        }
        
        # 选择操作类型
        op_frame = ttk.LabelFrame(self.special_ops_window, text="选择操作")
        op_frame.pack(padx=10, pady=5, fill="x")
        
        # 操作类型变量
        op_type = tk.StringVar(value="delete_rows")
        
        # 操作选项
        ops = [
            ("删除满足条件的行", "delete_rows"),
            ("替换特定值", "replace_values"),
            ("转换列数据类型", "convert_type"),
            ("处理缺失值", "handle_missing"),
            ("创建新列", "create_column"),
            ("创建数据透视表", "pivot_table"),
            ("排序数据", "sort_data"),
            ("数据归一化/标准化", "normalize_data"),
            ("多条件筛选", "multi_filter"),  # 添加多条件筛选选项
        ]
        
        # 创建双列布局
        for i, (text, value) in enumerate(ops):
            row = i // 2
            col = i % 2
            ttk.Radiobutton(
                op_frame, 
                text=text, 
                value=value, 
                variable=op_type
            ).grid(row=row, column=col, padx=15, pady=3, sticky="w")
        
        # 条件设置区域
        cond_frame = ttk.LabelFrame(self.special_ops_window, text="操作条件")
        cond_frame.pack(padx=10, pady=5, fill="both", expand=True)
        
        # 描述标签 - 显示当前操作的描述
        desc_var = tk.StringVar(value="删除满足指定条件的数据行")
        desc_label = ttk.Label(cond_frame, textvariable=desc_var, wraplength=650)
        desc_label.grid(row=0, column=0, columnspan=2, padx=5, pady=5, sticky="w")
        
        # 表达式提示函数
        def show_placeholder(event, entry_widget, placeholder_text):
            if not entry_widget.get():
                entry_widget.insert(0, placeholder_text)
                entry_widget.config(foreground='gray')
                
        def clear_placeholder(event, entry_widget, placeholder_text):
            if entry_widget.get() == placeholder_text:
                entry_widget.delete(0, tk.END)
                entry_widget.config(foreground='black')
        
        # ---------- 创建所有可能用到的UI元素 ----------
        
        # 1. 选择列
        col_label = ttk.Label(cond_frame, text="选择列:")
        col_label.grid(row=1, column=0, padx=5, pady=5, sticky="w")
        
        col_var = tk.StringVar()
        col_combo = ttk.Combobox(cond_frame, textvariable=col_var, width=30)
        col_combo['values'] = list(self.current_excel_df.columns)
        if len(self.current_excel_df.columns) > 0:
            col_var.set(self.current_excel_df.columns[0])
        col_combo.grid(row=1, column=1, padx=5, pady=5, sticky="ew")
        
        # 2. 操作符
        op_label = ttk.Label(cond_frame, text="操作符:")
        op_label.grid(row=2, column=0, padx=5, pady=5, sticky="w")
        
        op_var = tk.StringVar(value="==")
        op_combo = ttk.Combobox(cond_frame, textvariable=op_var, width=15)
        op_combo['values'] = ["==", ">", "<", ">=", "<=", "!=", "包含", "开始于", "结束于"]
        op_combo.grid(row=2, column=1, padx=5, pady=5, sticky="w")
        
        # 3. 值输入
        val_label = ttk.Label(cond_frame, text="值:")
        val_label.grid(row=3, column=0, padx=5, pady=5, sticky="w")
        
        val_var = tk.StringVar()
        val_entry = ttk.Entry(cond_frame, textvariable=val_var, width=35)
        val_entry.grid(row=3, column=1, padx=5, pady=5, sticky="ew")
        
        # 值输入占位符
        val_placeholder = "输入筛选值..."
        val_entry.bind("<FocusIn>", lambda e: clear_placeholder(e, val_entry, val_placeholder))
        val_entry.bind("<FocusOut>", lambda e: show_placeholder(e, val_entry, val_placeholder))
        show_placeholder(None, val_entry, val_placeholder)
        
        # 4. 新值输入
        new_val_label = ttk.Label(cond_frame, text="新值:")
        new_val_label.grid(row=4, column=0, padx=5, pady=5, sticky="w")
        
        new_val_var = tk.StringVar()
        new_val_entry = ttk.Entry(cond_frame, textvariable=new_val_var, width=35)
        new_val_entry.grid(row=4, column=1, padx=5, pady=5, sticky="ew")
        
        # 新值输入占位符
        new_val_placeholder = "输入替换值..."
        new_val_entry.bind("<FocusIn>", lambda e: clear_placeholder(e, new_val_entry, new_val_placeholder))
        new_val_entry.bind("<FocusOut>", lambda e: show_placeholder(e, new_val_entry, new_val_placeholder))
        show_placeholder(None, new_val_entry, new_val_placeholder)
        
        # 5. 数据类型转换下拉框
        type_var = tk.StringVar(value="str")
        type_combo = ttk.Combobox(cond_frame, textvariable=type_var, width=15)
        type_combo['values'] = ["int", "float", "str", "datetime", "category", "bool"]
        type_combo.grid(row=3, column=1, padx=5, pady=5, sticky="w")
        type_combo.grid_remove()  # 初始隐藏
        
        # 6. 数据透视表特定选项
        # 索引列
        index_label = ttk.Label(cond_frame, text="索引列:")
        index_label.grid(row=5, column=0, padx=5, pady=5, sticky="w")
        index_label.grid_remove()  # 初始隐藏
        
        index_var = tk.StringVar()
        index_combo = ttk.Combobox(cond_frame, textvariable=index_var, width=30)
        index_combo['values'] = list(self.current_excel_df.columns)
        index_combo.grid(row=5, column=1, padx=5, pady=5, sticky="ew")
        index_combo.grid_remove()  # 初始隐藏
        
        # 数值列
        values_label = ttk.Label(cond_frame, text="数值列:")
        values_label.grid(row=6, column=0, padx=5, pady=5, sticky="w")
        values_label.grid_remove()  # 初始隐藏
        
        values_var = tk.StringVar()
        values_combo = ttk.Combobox(cond_frame, textvariable=values_var, width=30)
        # 所有列
        all_cols = list(self.current_excel_df.columns)
        values_combo['values'] = all_cols
        if all_cols:
            values_var.set(all_cols[0])
        values_combo.grid(row=6, column=1, padx=5, pady=5, sticky="ew")
        values_combo.grid_remove()  # 初始隐藏
        
        # 聚合函数
        agg_label = ttk.Label(cond_frame, text="聚合函数:")
        agg_label.grid(row=7, column=0, padx=5, pady=5, sticky="w")
        agg_label.grid_remove()  # 初始隐藏
        
        agg_var = tk.StringVar(value="mean")
        agg_combo = ttk.Combobox(cond_frame, textvariable=agg_var, width=15)
        agg_combo['values'] = ["mean", "sum", "count", "min", "max", "first", "last"]
        agg_combo.grid(row=7, column=1, padx=5, pady=5, sticky="w")
        agg_combo.grid_remove()  # 初始隐藏
        
        # 7. 排序特定选项
        sort_order_label = ttk.Label(cond_frame, text="排序方式:")
        sort_order_label.grid(row=8, column=0, padx=5, pady=5, sticky="w")
        sort_order_label.grid_remove()  # 初始隐藏
        
        sort_order_var = tk.StringVar(value="升序")
        sort_order_combo = ttk.Combobox(cond_frame, textvariable=sort_order_var, width=15)
        sort_order_combo['values'] = ["升序", "降序"]
        sort_order_combo.grid(row=8, column=1, padx=5, pady=5, sticky="w")
        sort_order_combo.grid_remove()  # 初始隐藏
        
        # 8. 归一化特定选项
        normalize_method_label = ttk.Label(cond_frame, text="归一化方法:")
        normalize_method_label.grid(row=9, column=0, padx=5, pady=5, sticky="w")
        normalize_method_label.grid_remove()  # 初始隐藏
        
        normalize_method_var = tk.StringVar(value="min-max")
        normalize_method_combo = ttk.Combobox(cond_frame, textvariable=normalize_method_var, width=15)
        normalize_method_combo['values'] = ["min-max", "z-score", "robust"]
        normalize_method_combo.grid(row=9, column=1, padx=5, pady=5, sticky="w")
        normalize_method_combo.grid_remove()  # 初始隐藏
        
        # 9. 表达式提示标签
        expr_tip_label = ttk.Label(cond_frame, text="表达式示例", font=("", 9, "italic"))
        expr_tip_label.grid(row=10, column=0, padx=5, pady=0, sticky="w")
        expr_tip_label.grid_remove()  # 初始隐藏
        
        expr_tip_var = tk.StringVar()
        expr_tip_text = ttk.Label(cond_frame, textvariable=expr_tip_var, wraplength=650, 
                                  font=("", 9, "italic"), foreground="gray")
        expr_tip_text.grid(row=10, column=1, padx=5, pady=0, sticky="w")
        expr_tip_text.grid_remove()  # 初始隐藏
        
        # 预览区域
        preview_frame = ttk.LabelFrame(self.special_ops_window, text="数据预览")
        preview_frame.pack(padx=10, pady=5, fill="both", expand=True)
        
        preview_text = scrolledtext.ScrolledText(preview_frame, height=10, wrap=tk.WORD)
        preview_text.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 显示数据预览
        if len(self.current_excel_df) > 0:
            preview = self.current_excel_df.head(5).to_string()
            preview_text.insert(tk.END, f"原始数据预览(前5行):\n{preview}")
        
        # 按钮区域
        button_frame = ttk.Frame(self.special_ops_window)
        button_frame.pack(fill="x", padx=10, pady=10)
        
        # 导出配置按钮
        export_config_btn = ttk.Button(
            button_frame, 
            text="导出配置", 
            command=self.export_operation_config
        )
        export_config_btn.pack(side="left", padx=5)
        
        # 导入配置按钮
        import_config_btn = ttk.Button(
            button_frame, 
            text="导入配置", 
            command=self.import_operation_config
        )
        import_config_btn.pack(side="left", padx=5)
        
        # 测试按钮 - 提供操作预览
        test_btn = ttk.Button(
            button_frame, 
            text="测试操作", 
            command=lambda: self._preview_operation(
                op_type.get(),
                col_var.get(),
                op_var.get(),
                val_var.get() if val_var.get() != val_placeholder else "",
                new_val_var.get() if new_val_var.get() != new_val_placeholder else "",
                index_var.get(),
                values_var.get(),
                agg_var.get(),
                sort_order_var.get(),
                normalize_method_var.get(),
                preview_text
            )
        )
        test_btn.pack(side="left", padx=5)
        
        # 执行按钮
        apply_btn = ttk.Button(
            button_frame, 
            text="执行操作", 
            command=lambda: self.execute_special_operation(
                op_type.get(), 
                col_var.get(), 
                op_var.get(), 
                val_var.get() if val_var.get() != val_placeholder else "", 
                new_val_var.get() if new_val_var.get() != new_val_placeholder else "",
                index_var.get(),
                values_var.get(),
                agg_var.get(),
                sort_order_var.get(),
                normalize_method_var.get()
            )
        )
        apply_btn.pack(side="left", padx=5)
        
        # 取消按钮
        cancel_btn = ttk.Button(
            button_frame,
            text="取消",
            command=self.special_ops_window.destroy
        )
        cancel_btn.pack(side="right", padx=5)
        
        # 设置列权重
        cond_frame.columnconfigure(1, weight=1)
        
        # 更新UI状态的函数 - 这是关键部分
        def update_ui_state(*args):
            op = op_type.get()
            
            # 更新描述
            if op in op_descriptions:
                desc_var.set(op_descriptions[op])
            
            # 首先隐藏所有控件
            col_label.grid_remove()
            col_combo.grid_remove()
            op_label.grid_remove()
            op_combo.grid_remove()
            val_label.grid_remove()
            val_entry.grid_remove()
            new_val_label.grid_remove()
            new_val_entry.grid_remove()
            index_label.grid_remove()
            index_combo.grid_remove()
            values_label.grid_remove()
            values_combo.grid_remove()
            agg_label.grid_remove()
            agg_combo.grid_remove()
            sort_order_label.grid_remove()
            sort_order_combo.grid_remove()
            normalize_method_label.grid_remove()
            normalize_method_combo.grid_remove()
            expr_tip_label.grid_remove()
            expr_tip_text.grid_remove()
            type_combo.grid_remove()
            
            # 如果存在多条件筛选框架，则隐藏它
            if hasattr(self, 'multi_filter_frame') and self.multi_filter_frame is not None:
                if self.multi_filter_frame.winfo_ismapped():
                    self.multi_filter_frame.grid_remove()
            
            # 重置标签文本
            col_label.configure(text="选择列:")
            op_label.configure(text="操作符:")
            val_label.configure(text="值:")
            new_val_label.configure(text="新值:")
            
            # 重置占位符文本和文本颜色
            val_entry.delete(0, tk.END)
            val_entry.config(foreground='black')
            
            new_val_entry.delete(0, tk.END)
            new_val_entry.config(foreground='black')
            
            # 根据操作类型显示不同的控件组合
            if op == "delete_rows":
                # 显示列选择、操作符和值
                col_label.grid()
                col_combo.grid()
                op_label.grid()
                op_combo.grid()
                val_label.grid()
                val_entry.grid()
                
                # 更新操作符值
                op_combo['values'] = ["==", ">", "<", ">=", "<=", "!=", "包含", "开始于", "结束于"]
                if op_var.get() not in ["==", ">", "<", ">=", "<=", "!=", "包含", "开始于", "结束于"]:
                    op_var.set("==")
                    
                # 显示占位符
                show_placeholder(None, val_entry, "输入筛选值...")
                
            elif op == "replace_values":
                # 显示所有标准字段
                col_label.grid()
                col_combo.grid()
                op_label.grid()
                op_combo.grid()
                val_label.grid()
                val_entry.grid()
                new_val_label.grid()
                new_val_entry.grid()
                
                # 更新操作符值
                op_combo['values'] = ["==", ">", "<", ">=", "<=", "!=", "包含", "开始于", "结束于"]
                if op_var.get() not in ["==", ">", "<", ">=", "<=", "!=", "包含", "开始于", "结束于"]:
                    op_var.set("==")
                
                # 显示占位符
                show_placeholder(None, val_entry, "输入要替换的值...")
                show_placeholder(None, new_val_entry, "输入替换后的新值...")
                
            elif op == "convert_type":
                # 显示列选择和类型下拉框
                col_label.grid()
                col_combo.grid()
                val_label.grid()
                val_label.configure(text="目标类型:")
                type_combo.grid(row=3, column=1, padx=5, pady=5, sticky="w")
                
                # 设置默认类型
                if type_var.get() not in ["int", "float", "str", "datetime", "category", "bool"]:
                    type_var.set("str")
                
            elif op == "handle_missing":
                # 显示列选择、处理方式和填充值
                col_label.grid()
                col_combo.grid()
                op_label.grid()
                op_combo.grid()
                new_val_label.grid()
                new_val_entry.grid()
                
                # 更新操作符和标签
                op_label.configure(text="处理方式:")
                op_combo['values'] = ["填充", "删除", "插值"]
                if op_var.get() not in ["填充", "删除", "插值"]:
                    op_var.set("填充")
                
                # 更新填充值标签
                new_val_label.configure(text="填充值:")
                
                # 显示占位符
                show_placeholder(None, new_val_entry, "输入填充值，如0或空字符串...")
                
            elif op == "create_column":
                # 显示列选择、表达式类型、表达式和新列名
                col_label.grid()
                col_combo.grid()
                op_label.grid()
                op_combo.grid()
                val_label.grid()
                val_entry.grid()
                new_val_label.grid()
                new_val_entry.grid()
                expr_tip_label.grid()
                expr_tip_text.grid()
                
                # 更新标签和值
                op_label.configure(text="表达式类型:")
                op_combo['values'] = ["计算", "条件", "连接"]
                if op_var.get() not in ["计算", "条件", "连接"]:
                    op_var.set("计算")
                    
                val_label.configure(text="表达式:")
                new_val_label.configure(text="新列名:")
                
                # 更新表达式提示
                expr_type = op_var.get()
                if expr_type in expr_tips:
                    expr_tip_var.set(expr_tips[expr_type])
                else:
                    expr_tip_var.set(expr_tips["计算"])
                
                # 显示占位符
                show_placeholder(None, val_entry, "输入表达式...")
                show_placeholder(None, new_val_entry, "输入新列名...")
                
            elif op == "pivot_table":
                # 显示数据透视表相关控件
                col_label.grid()
                col_combo.grid()
                index_label.grid()
                index_combo.grid()
                values_label.grid()
                values_combo.grid()
                agg_label.grid()
                agg_combo.grid()
                
                # 更新标签
                col_label.configure(text="列标签(可选):")
                
                # 设置默认值
                if not index_var.get() and len(self.current_excel_df.columns) > 0:
                    # 尝试默认选择文本列作为索引和列
                    text_cols = self.current_excel_df.select_dtypes(include=['object']).columns.tolist()
                    if text_cols:
                        index_var.set(text_cols[0])
                        if len(text_cols) > 1:
                            col_var.set(text_cols[1])
                        else:
                            col_var.set("")
                    else:
                        index_var.set(self.current_excel_df.columns[0])
                        col_var.set("")
                
                # 设置默认聚合函数
                if agg_var.get() not in ["mean", "sum", "count", "min", "max", "first", "last"]:
                    agg_var.set("count")  # 默认使用计数
                
                # 确保选择了数值列
                if not values_var.get() and len(self.current_excel_df.columns) > 0:
                    values_var.set(self.current_excel_df.columns[0])
                
            elif op == "sort_data":
                # 显示列选择和排序方式
                col_label.grid()
                col_combo.grid()
                sort_order_label.grid()
                sort_order_combo.grid()
                
                # 设置默认排序方式
                if sort_order_var.get() not in ["升序", "降序"]:
                    sort_order_var.set("升序")
                
            elif op == "normalize_data":
                # 显示列选择和归一化方法
                col_label.grid()
                col_combo.grid()
                normalize_method_label.grid()
                normalize_method_combo.grid()
                
                # 设置数值列列表
                numeric_cols = self.current_excel_df.select_dtypes(include=[np.number]).columns.tolist()
                col_combo['values'] = numeric_cols
                
                # 如果当前选择不是数值列，则选择第一个数值列
                if col_var.get() not in numeric_cols and numeric_cols:
                    col_var.set(numeric_cols[0])
                
                # 设置默认归一化方法
                if normalize_method_var.get() not in ["min-max", "z-score", "robust"]:
                    normalize_method_var.set("min-max")
            
            elif op == "multi_filter":
                # 创建多条件筛选区域
                # 显示一个标签
                col_label.grid()
                col_label.configure(text="条件设置:")
                
                # 创建多条件筛选框架
                if self.multi_filter_frame is None:
                    # 创建新的框架来容纳条件
                    self.multi_filter_frame = ttk.Frame(cond_frame)
                    self.multi_filter_frame.grid(row=1, column=1, padx=5, pady=5, sticky="ew")
                    
                    # 添加按钮
                    add_btn = ttk.Button(
                        self.multi_filter_frame, 
                        text="添加条件", 
                        command=self._add_filter_condition
                    )
                    add_btn.pack(fill="x", pady=5)
                    
                    # 添加逻辑关系选择
                    logic_frame = ttk.Frame(self.multi_filter_frame)
                    logic_frame.pack(fill="x", pady=5)
                    
                    logic_label = ttk.Label(logic_frame, text="条件关系:")
                    logic_label.pack(side="left", padx=5)
                    
                    logic_and = ttk.Radiobutton(logic_frame, text="满足所有条件(AND)", 
                                                variable=self.logic_var, value="AND")
                    logic_and.pack(side="left", padx=5)
                    
                    logic_or = ttk.Radiobutton(logic_frame, text="满足任一条件(OR)", 
                                               variable=self.logic_var, value="OR")
                    logic_or.pack(side="left", padx=5)
                    
                    # 初始添加一个条件
                    if not self.filter_conditions:
                        self._add_filter_condition()
                else:
                    # 如果框架已存在，显示它
                    self.multi_filter_frame.grid(row=1, column=1, padx=5, pady=5, sticky="ew")
        
        # 绑定操作符变更事件，更新表达式提示
        def update_expr_tip(*args):
            expr_type = op_var.get()
            if expr_type in expr_tips and op_type.get() == "create_column":
                expr_tip_var.set(expr_tips[expr_type])
        
        # 绑定变更事件
        op_type.trace("w", update_ui_state)  # 操作类型变更时更新UI
        op_var.trace("w", update_expr_tip)   # 表达式类型变更时更新提示
        
        # 初始化UI状态
        update_ui_state()
    
    def _preview_operation(self, op_type, column, operator, value, new_value, 
                          index, values, aggregation, sort_order, normalize_method,
                          preview_text):
        """预览数据操作效果"""
        try:
            preview_text.delete("1.0", tk.END)
            
            # 检查参数
            if not column and op_type not in ["pivot_table"]:
                preview_text.insert(tk.END, "错误: 请选择列\n")
                return
                
            if op_type == "delete_rows" and not value:
                preview_text.insert(tk.END, "错误: 请输入筛选值\n")
                return
                
            if op_type == "create_column" and not value:
                preview_text.insert(tk.END, "错误: 请输入表达式\n")
                return
                
            if op_type == "create_column" and not new_value:
                preview_text.insert(tk.END, "错误: 请输入新列名\n")
                return
                
            if op_type == "pivot_table" and (not index or not values):
                preview_text.insert(tk.END, "错误: 请选择索引列和数值列\n")
                return
            
            # 显示原始数据预览
            original_preview = self.current_excel_df.head(3).to_string()
            preview_text.insert(tk.END, f"原始数据预览(前3行):\n{original_preview}\n\n")
            
            # 根据操作类型执行预览
            if op_type == "delete_rows":
                # 创建预览掩码
                df = self.current_excel_df.copy()
                try:
                    if df[column].dtype.kind in 'ifc':
                        value = float(value)
                except (ValueError, TypeError):
                    pass
                
                mask = self._apply_operator(df, column, operator, value)
                deleted_count = mask.sum()
                result_count = len(df) - deleted_count
                
                # 显示将被删除的行
                if deleted_count > 0:
                    deleted_preview = df[mask].head(3).to_string()
                    preview_text.insert(tk.END, f"将删除的行(预览):\n{deleted_preview}\n\n")
                else:
                    preview_text.insert(tk.END, "没有数据行满足删除条件\n\n")
                
                preview_text.insert(tk.END, f"总共将删除: {deleted_count}行\n")
                preview_text.insert(tk.END, f"操作后剩余: {result_count}行\n")
                
            elif op_type == "replace_values":
                df = self.current_excel_df.copy()
                try:
                    if df[column].dtype.kind in 'ifc':
                        value = float(value)
                        if new_value:
                            new_value = float(new_value)
                except (ValueError, TypeError):
                    pass
                
                mask = self._apply_operator(df, column, operator, value)
                affected_count = mask.sum()
                
                # 预览一些将被替换的值
                if affected_count > 0:
                    preview_text.insert(tk.END, f"将进行的替换(预览):\n")
                    
                    # 显示前几个替换示例
                    affected_df = df[mask].head(3)
                    for idx, row in affected_df.iterrows():
                        old_val = row[column]
                        preview_text.insert(tk.END, f"行 {idx}: '{old_val}' -> '{new_value}'\n")
                else:
                    preview_text.insert(tk.END, "没有数据满足替换条件\n")
                
                preview_text.insert(tk.END, f"\n总共将替换: {affected_count}个值\n")
                
            elif op_type == "convert_type":
                df = self.current_excel_df.copy()
                
                # 获取当前类型
                current_type = df[column].dtype
                
                # 显示类型转换信息
                preview_text.insert(tk.END, f"列 '{column}' 的类型转换:\n")
                preview_text.insert(tk.END, f"当前类型: {current_type}\n")
                preview_text.insert(tk.END, f"目标类型: {value}\n\n")
                
                # 显示一些转换示例
                preview_text.insert(tk.END, "转换示例:\n")
                
                sample_values = df[column].head(3).tolist()
                for i, val in enumerate(sample_values):
                    preview_text.insert(tk.END, f"{i+1}. 原值: '{val}' (类型: {type(val).__name__})\n")
                    
                    try:
                        if value == "int":
                            converted = int(float(val)) if pd.notna(val) else None
                        elif value == "float":
                            converted = float(val) if pd.notna(val) else None
                        elif value == "str":
                            converted = str(val) if pd.notna(val) else ""
                        elif value == "datetime":
                            converted = pd.to_datetime(val) if pd.notna(val) else None
                        elif value == "bool":
                            converted = bool(val) if pd.notna(val) else None
                        else:
                            converted = val
                            
                        preview_text.insert(tk.END, f"   转换后: '{converted}' (类型: {type(converted).__name__})\n")
                    except Exception as e:
                        preview_text.insert(tk.END, f"   转换失败: {str(e)}\n")
                
            elif op_type == "handle_missing":
                df = self.current_excel_df.copy()
                
                # 计算缺失值数量
                missing_count = df[column].isna().sum()
                missing_percent = (missing_count / len(df)) * 100 if len(df) > 0 else 0
                
                preview_text.insert(tk.END, f"列 '{column}' 的缺失值处理:\n")
                preview_text.insert(tk.END, f"缺失值数量: {missing_count} ({missing_percent:.2f}%)\n")
                preview_text.insert(tk.END, f"处理方式: {operator}\n")
                
                if operator == "填充":
                    preview_text.insert(tk.END, f"填充值: '{new_value}'\n")
                
                # 显示一些缺失值位置
                if missing_count > 0:
                    missing_indices = df[df[column].isna()].index.tolist()[:3]
                    preview_text.insert(tk.END, "\n缺失值位置示例:\n")
                    for idx in missing_indices:
                        preview_text.insert(tk.END, f"行索引: {idx}\n")
                else:
                    preview_text.insert(tk.END, "\n列中没有缺失值\n")
                
            elif op_type == "create_column":
                preview_text.insert(tk.END, f"创建新列 '{new_value}':\n")
                preview_text.insert(tk.END, f"基于列: '{column}'\n")
                preview_text.insert(tk.END, f"表达式类型: {operator}\n")
                preview_text.insert(tk.END, f"表达式: {value}\n\n")
                
                try:
                    # 尝试模拟计算结果
                    if operator == "计算":
                        # 安全地验证表达式
                        df = self.current_excel_df.copy()
                        safe_expr = value
                        for col in df.columns:
                            # 使用正则表达式确保只替换完整的列名
                            if col in safe_expr:
                                pattern = r'\b' + re.escape(col) + r'\b'
                                safe_col = f"df['{col}']"
                                safe_expr = re.sub(pattern, safe_col, safe_expr)
                                
                        # 尝试计算前几行结果
                        try:
                            result = eval(safe_expr)
                            preview_text.insert(tk.END, "计算结果预览(前3行):\n")
                            if isinstance(result, pd.Series):
                                for i, val in enumerate(result.head(3)):
                                    preview_text.insert(tk.END, f"{i}: {val}\n")
                            else:
                                preview_text.insert(tk.END, f"结果: {result}\n")
                        except Exception as e:
                            preview_text.insert(tk.END, f"表达式计算错误: {str(e)}\n")
                    elif operator == "条件":
                        preview_text.insert(tk.END, "此操作将创建一个新列，满足条件的行值为1，不满足的为0\n")
                    elif operator == "连接":
                        preview_text.insert(tk.END, "此操作将连接多列或字符串常量\n")
                        
                except Exception as e:
                    preview_text.insert(tk.END, f"表达式预览错误: {str(e)}\n")
                
            elif op_type == "pivot_table":
                preview_text.insert(tk.END, f"创建数据透视表:\n")
                preview_text.insert(tk.END, f"索引列: '{index}'\n")
                
                if column:
                    preview_text.insert(tk.END, f"列标签: '{column}'\n")
                
                preview_text.insert(tk.END, f"数值列: '{values}'\n")
                preview_text.insert(tk.END, f"聚合函数: {aggregation}\n\n")
                
                # 计算透视表大小
                df = self.current_excel_df.copy()
                index_unique = df[index].nunique()
                col_unique = df[column].nunique() if column else 0
                
                preview_text.insert(tk.END, f"预计透视表大小: {index_unique}行")
                if column:
                    preview_text.insert(tk.END, f" x {col_unique}列")
                preview_text.insert(tk.END, "\n")
                
                # 尝试创建简化版的透视表预览
                try:
                    if column:
                        pivot = pd.pivot_table(
                            df.head(min(100, len(df))), 
                            values=values, 
                            index=[index], 
                            columns=[column], 
                            aggfunc=aggregation
                        )
                        if not pivot.empty:
                            pivot_preview = pivot.head(3).to_string()
                            preview_text.insert(tk.END, f"透视表预览(前3行):\n{pivot_preview}\n")
                    else:
                        pivot = pd.pivot_table(
                            df.head(min(100, len(df))), 
                            values=values, 
                            index=[index], 
                            aggfunc=aggregation
                        )
                        if not pivot.empty:
                            pivot_preview = pivot.head(3).to_string()
                            preview_text.insert(tk.END, f"透视表预览(前3行):\n{pivot_preview}\n")
                except Exception as e:
                    preview_text.insert(tk.END, f"透视表预览生成失败: {str(e)}\n")
                
            elif op_type == "sort_data":
                preview_text.insert(tk.END, f"对数据排序:\n")
                preview_text.insert(tk.END, f"排序列: '{column}'\n")
                preview_text.insert(tk.END, f"排序方式: {sort_order}\n\n")
                
                # 显示排序前后的对比
                df = self.current_excel_df.copy()
                is_ascending = (sort_order == "升序")
                
                preview_text.insert(tk.END, "排序前(前3行):\n")
                preview_text.insert(tk.END, f"{df.head(3).to_string()}\n\n")
                
                # 显示排序后的预览
                try:
                    sorted_df = df.sort_values(by=column, ascending=is_ascending)
                    preview_text.insert(tk.END, "排序后(前3行):\n")
                    preview_text.insert(tk.END, f"{sorted_df.head(3).to_string()}\n")
                except Exception as e:
                    preview_text.insert(tk.END, f"排序失败: {str(e)}\n")
                
            elif op_type == "normalize_data":
                preview_text.insert(tk.END, f"数据归一化/标准化:\n")
                preview_text.insert(tk.END, f"处理列: '{column}'\n")
                preview_text.insert(tk.END, f"方法: {normalize_method}\n\n")
                
                # 显示原始数据统计
                df = self.current_excel_df.copy()
                
                try:
                    # 检查列是否为数值类型
                    if not pd.api.types.is_numeric_dtype(df[column]):
                        preview_text.insert(tk.END, f"错误: 列 '{column}' 不是数值类型，无法进行归一化\n")
                        return
                        
                    stats = df[column].describe()
                    preview_text.insert(tk.END, f"原始数据统计:\n")
                    preview_text.insert(tk.END, f"最小值: {stats['min']:.4f}\n")
                    preview_text.insert(tk.END, f"最大值: {stats['max']:.4f}\n")
                    preview_text.insert(tk.END, f"平均值: {stats['mean']:.4f}\n")
                    preview_text.insert(tk.END, f"标准差: {stats['std']:.4f}\n\n")
                    
                    # 计算归一化后的预览
                    if normalize_method == "min-max":
                        # 处理最大最小值相同的情况
                        if stats['max'] == stats['min']:
                            preview_text.insert(tk.END, "警告: 所有值都相同，Min-Max归一化将使所有值变为0.5\n")
                            normalized = pd.Series([0.5] * len(df[column]))
                        else:
                            normalized = (df[column] - stats['min']) / (stats['max'] - stats['min'])
                            
                        new_stats = normalized.describe()
                        preview_text.insert(tk.END, f"归一化后统计(MinMax):\n")
                        preview_text.insert(tk.END, f"最小值: {new_stats['min']:.4f}\n")
                        preview_text.insert(tk.END, f"最大值: {new_stats['max']:.4f}\n")
                        preview_text.insert(tk.END, f"平均值: {new_stats['mean']:.4f}\n")
                    elif normalize_method == "z-score":
                        # 处理标准差为0的情况
                        if stats['std'] == 0:
                            preview_text.insert(tk.END, "警告: 标准差为0，Z-score标准化将使所有值变为0\n")
                            normalized = pd.Series([0] * len(df[column]))
                        else:
                            normalized = (df[column] - stats['mean']) / stats['std']
                            
                        new_stats = normalized.describe()
                        preview_text.insert(tk.END, f"标准化后统计(Z-Score):\n")
                        preview_text.insert(tk.END, f"平均值: {new_stats['mean']:.4f}\n")
                        preview_text.insert(tk.END, f"标准差: {new_stats['std']:.4f}\n")
                    elif normalize_method == "robust":
                        # 计算中位数和IQR
                        median = df[column].median()
                        q1 = df[column].quantile(0.25)
                        q3 = df[column].quantile(0.75)
                        iqr = q3 - q1
                        
                        if iqr == 0:
                            preview_text.insert(tk.END, "警告: IQR为0，稳健归一化将使所有值变为0\n")
                            normalized = pd.Series([0] * len(df[column]))
                        else:
                            normalized = (df[column] - median) / iqr
                            
                        new_stats = normalized.describe()
                        preview_text.insert(tk.END, f"稳健归一化后统计:\n")
                        preview_text.insert(tk.END, f"中位数: {new_stats['50%']:.4f}\n")
                        preview_text.insert(tk.END, f"四分位差: {new_stats['75%'] - new_stats['25%']:.4f}\n")
                
                except Exception as e:
                    preview_text.insert(tk.END, f"统计计算失败: {str(e)}\n")
            
            elif op_type == "multi_filter":
                # 获取所有筛选条件
                filter_conditions = []
                
                # 收集所有设置的条件
                if hasattr(self, 'filter_conditions') and isinstance(self.filter_conditions, list):
                    # 筛选出有效的条件框架
                    valid_conditions = []
                    for condition_frame in self.filter_conditions:
                        # 检查框架是否仍然有效
                        if not hasattr(condition_frame, 'winfo_exists') or not condition_frame.winfo_exists():
                            continue
                        
                        valid_conditions.append(condition_frame)
                        
                        # 从每个条件框中提取条件
                        widgets = condition_frame.winfo_children()
                        col_combo = None
                        op_combo = None
                        val_entry = None
                        
                        # 识别控件
                        for w in widgets:
                            if isinstance(w, ttk.Combobox):
                                # 第一个下拉框是列选择
                                if col_combo is None:
                                    col_combo = w
                                else:
                                    op_combo = w
                            elif isinstance(w, ttk.Entry):
                                val_entry = w
                            
                            if col_combo and op_combo and val_entry:
                                col = col_combo.get()
                                op = op_combo.get()
                                val = val_entry.get()
                                if col and op:
                                    filter_conditions.append((col, op, val))
                    
                    # 更新有效条件框架列表
                    self.filter_conditions = valid_conditions
                
                # 验证条件
                if not filter_conditions:
                    preview_text.insert(tk.END, "请至少添加一个筛选条件\n")
                    return
                
                # 显示条件信息
                preview_text.insert(tk.END, "多条件筛选设置:\n")
                preview_text.insert(tk.END, f"条件关系: {'所有条件都满足 (AND)' if self.logic_var.get() == 'AND' else '任一条件满足 (OR)'}\n\n")
                
                # 确保数据框有效
                if self.current_excel_df is not None:
                    # 应用条件进行预览
                    df = self.current_excel_df.copy()
                    masks = []
                    
                    # 处理每个条件
                    for col, op, val in filter_conditions:
                        preview_text.insert(tk.END, f"条件: {col} {op} {val}\n")
                        
                        # 对数值列进行适当的值转换
                        try:
                            if df[col].dtype.kind in 'ifc':
                                val = float(val)
                        except (ValueError, TypeError):
                            pass
                        
                        # 创建单个条件的掩码
                        mask = self._apply_operator(df, col, op, val)
                        masks.append(mask)
                    
                    # 组合所有条件
                    if masks:
                        if self.logic_var.get() == "AND":
                            final_mask = masks[0]
                            for mask in masks[1:]:
                                final_mask = final_mask & mask
                        else:  # OR
                            final_mask = masks[0]
                            for mask in masks[1:]:
                                final_mask = final_mask | mask
                        
                        # 计算筛选后剩余数据量
                        filtered_count = final_mask.sum()
                        
                        # 显示筛选结果预览
                        if filtered_count > 0:
                            filtered_preview = df[final_mask].head(3).to_string()
                            preview_text.insert(tk.END, f"\n筛选结果预览(共{filtered_count}行，显示前3行):\n{filtered_preview}\n")
                        else:
                            preview_text.insert(tk.END, "\n没有数据满足筛选条件\n")
                else:
                    preview_text.insert(tk.END, "无法生成预览：未加载数据")
        
        except Exception as e:
            preview_text.insert(tk.END, f"预览时出错: {str(e)}\n")
            traceback.print_exc()
        
        # 滚动到顶部
        preview_text.see("1.0")
    
    def execute_special_operation(self, op_type, column, operator, value, new_value, 
                                index=None, values=None, aggregation=None, 
                                sort_order=None, normalize_method=None):
        """执行特殊数据操作"""
        if self.current_excel_df is None:
            self.update_chat("请先加载数据")
            return
            
        if not column and op_type not in ["pivot_table"]:
            self.update_chat("请选择有效的列")
            return
        
        try:
            original_size = len(self.current_excel_df)
            result_df = None
            operation_desc = ""
            
            # 根据操作类型执行不同的操作
            if op_type == "delete_rows":
                if not value:
                    self.update_chat("请输入筛选值")
                    return
                result_df, operation_desc = self._delete_rows(column, operator, value)
                
            elif op_type == "replace_values":
                if value is None:
                    self.update_chat("请输入要替换的值")
                    return
                result_df, operation_desc = self._replace_values(column, operator, value, new_value)
                
            elif op_type == "convert_type":
                if not value and op_type == "convert_type":
                    # 查看type_combo的值
                    for widget in self.special_ops_window.winfo_children():
                        if isinstance(widget, ttk.LabelFrame) and widget['text'] == "操作条件":
                            for child in widget.winfo_children():
                                if isinstance(child, ttk.Combobox) and child.grid_info() and child.grid_info()['row'] == 3:
                                    value = child.get()
                                    break
                
                if not value:
                    self.update_chat("请选择目标数据类型")
                    return
                    
                result_df, operation_desc = self._convert_column_type(column, value)
                
            elif op_type == "handle_missing":
                result_df, operation_desc = self._handle_missing_values(column, operator, new_value)
                
            elif op_type == "create_column":
                if not value:
                    self.update_chat("请输入表达式")
                    return
                if not new_value:
                    self.update_chat("请输入新列名")
                    return
                result_df, operation_desc = self._create_new_column(column, operator, value, new_value)
                
            elif op_type == "pivot_table":
                if not index:
                    self.update_chat("请选择索引列")
                    return
                if not values:
                    self.update_chat("请选择数值列")
                    return
                result_df, operation_desc = self._create_pivot_table(index, column, values, aggregation)
                
            elif op_type == "sort_data":
                if not column:
                    self.update_chat("请选择排序列")
                    return
                result_df, operation_desc = self._sort_data(column, sort_order)
                
            elif op_type == "normalize_data":
                if not column:
                    self.update_chat("请选择要归一化的列")
                    return
                result_df, operation_desc = self._normalize_data(column, normalize_method)
            
            elif op_type == "multi_filter":
                # 获取所有筛选条件
                filter_conditions = []
                
                # 收集所有设置的条件
                if hasattr(self, 'filter_conditions') and isinstance(self.filter_conditions, list):
                    # 筛选出有效的条件框架
                    valid_conditions = []
                    for condition_frame in self.filter_conditions:
                        # 检查框架是否仍然有效
                        if not hasattr(condition_frame, 'winfo_exists') or not condition_frame.winfo_exists():
                            continue
                        
                        valid_conditions.append(condition_frame)
                        
                        # 从每个条件框中提取条件
                        widgets = condition_frame.winfo_children()
                        col_combo = None
                        op_combo = None
                        val_entry = None
                        
                        # 识别控件
                        for w in widgets:
                            if isinstance(w, ttk.Combobox):
                                # 第一个下拉框是列选择
                                if col_combo is None:
                                    col_combo = w
                                else:
                                    op_combo = w
                            elif isinstance(w, ttk.Entry):
                                val_entry = w
                            
                            if col_combo and op_combo and val_entry:
                                col = col_combo.get()
                                op = op_combo.get()
                                val = val_entry.get()
                                if col and op:
                                    filter_conditions.append((col, op, val))
                    
                    # 更新有效条件框架列表
                    self.filter_conditions = valid_conditions
                
                # 验证条件
                if not filter_conditions:
                    self.update_chat("请至少添加一个筛选条件")
                    return
                
                # 获取逻辑关系
                logic_relation = self.logic_var.get()
                
                try:
                    # 应用筛选
                    result_df, operation_desc = self._apply_multi_filter(filter_conditions, logic_relation)
                    
                    # 更新当前DataFrame为结果
                    self.current_excel_df = result_df
                    self.pai_df = pai.DataFrame(result_df)
                    
                    # 设置文件修改标志
                    self._file_modified = True
                    
                    # 显示操作结果
                    self.update_chat(operation_desc)
                    
                    # 显示保存结果按钮
                    self.show_result_buttons()
                    
                    # 关闭特殊操作窗口
                    if self.special_ops_window and self.special_ops_window.winfo_exists():
                        self.special_ops_window.destroy()
                        
                    # 清理状态变量
                    self.multi_filter_frame = None
                    self.filter_conditions = []
                    
                except Exception as e:
                    self.update_chat(f"筛选操作失败: {str(e)}")
                    traceback.print_exc()
            
            else:
                self.update_chat(f"未知的操作类型: {op_type}")
                return
            
            if result_df is not None:
                # 更新当前DataFrame
                self.current_excel_df = result_df
                self.pai_df = pai.DataFrame(result_df)
                
                # 设置文件修改标志
                self._file_modified = True
                
                # 显示操作结果
                new_size = len(result_df)
                self.update_chat(f"{operation_desc}")
                self.update_chat(f"操作前数据: {original_size}行，操作后: {new_size}行")
                
                # 关闭特殊操作窗口
                if self.special_ops_window and self.special_ops_window.winfo_exists():
                    self.special_ops_window.destroy()
                
                # 显示预览
                if len(result_df) > 0:
                    preview = result_df.head(5).to_string()
                    self.update_chat(f"结果预览:\n{preview}")
                
                # 显示保存按钮
                self.show_result_buttons()
            
        except Exception as e:
            error_msg = str(e)
            self.update_chat(f"操作失败: {error_msg}")
            
            # 显示更详细的错误信息
            if "KeyError" in error_msg:
                self.update_chat("可能的原因: 指定的列名不存在")
            elif "ValueError" in error_msg:
                self.update_chat("可能的原因: 数据类型不匹配或无效的参数值")
            elif "SyntaxError" in error_msg:
                self.update_chat("可能的原因: 表达式语法错误")
                
            traceback.print_exc()
    
    def _delete_rows(self, column, operator, value):
        """删除满足条件的行"""
        df = self.current_excel_df.copy()
        
        # 解析值
        try:
            # 尝试转换为数值
            if df[column].dtype.kind in 'ifc':  # 整数、浮点或复数
                value = float(value)
        except (ValueError, TypeError):
            # 保持为字符串
            pass
        
        # 应用操作符
        mask = self._apply_operator(df, column, operator, value)
        
        # 删除匹配的行
        result = df[~mask]  # 取反，保留不匹配的行
        
        return result, f"已删除满足条件的行: {column} {operator} {value}"
    
    def _replace_values(self, column, operator, value, new_value):
        """替换满足条件的值"""
        df = self.current_excel_df.copy()
        
        # 尝试进行类型转换
        try:
            if df[column].dtype.kind in 'ifc':
                value = float(value)
                new_value = float(new_value)
        except (ValueError, TypeError):
            # 保持为字符串
            pass
        
        # 应用操作符获取要替换的行
        mask = self._apply_operator(df, column, operator, value)
        
        # 替换值
        df.loc[mask, column] = new_value
        
        return df, f"已将满足条件的值替换: {column} {operator} {value} -> {new_value}"
    
    def _convert_column_type(self, column, target_type):
        """转换列的数据类型"""
        df = self.current_excel_df.copy()
        
        # 根据目标类型执行转换
        try:
            if target_type == "int":
                # 对于整数，先转换为float处理NA，再转换为int
                df[column] = pd.to_numeric(df[column], errors='coerce').fillna(0).astype(int)
            elif target_type == "float":
                df[column] = pd.to_numeric(df[column], errors='coerce')
            elif target_type == "str":
                df[column] = df[column].astype(str)
            elif target_type == "datetime":
                df[column] = pd.to_datetime(df[column], errors='coerce')
            elif target_type == "category":
                df[column] = df[column].astype('category')
            elif target_type == "bool":
                df[column] = df[column].astype(bool)
        except Exception as e:
            raise Exception(f"类型转换失败: {str(e)}")
        
        return df, f"已将列 '{column}' 转换为 {target_type} 类型"
    
    def _handle_missing_values(self, column, method, fill_value):
        """处理缺失值"""
        df = self.current_excel_df.copy()
        
        # 计算缺失值数量
        missing_count = df[column].isna().sum()
        
        if method == "填充":
            # 尝试转换填充值类型
            try:
                if df[column].dtype.kind in 'ifc':
                    fill_value = float(fill_value)
            except (ValueError, TypeError):
                # 保持为字符串
                pass
            
            df[column] = df[column].fillna(fill_value)
            operation_desc = f"已将列 '{column}' 的 {missing_count} 个缺失值填充为 {fill_value}"
            
        elif method == "删除":
            df = df.dropna(subset=[column])
            operation_desc = f"已删除列 '{column}' 中包含缺失值的 {missing_count} 行数据"
            
        elif method == "插值":
            # 只对数值列进行插值
            if pd.api.types.is_numeric_dtype(df[column]):
                df[column] = df[column].interpolate(method='linear')
                operation_desc = f"已对列 '{column}' 的 {missing_count} 个缺失值进行线性插值"
            else:
                # 非数值列使用前向填充
                df[column] = df[column].fillna(method='ffill')
                operation_desc = f"已对列 '{column}' 的 {missing_count} 个缺失值进行前向填充"
        
        return df, operation_desc
    
    def _create_new_column(self, base_column, expr_type, expression, new_column_name):
        """创建新列"""
        df = self.current_excel_df.copy()
        
        if not new_column_name:
            raise ValueError("请指定新列名称")
        
        if expr_type == "计算":
            # 简单的算术表达式，如 "列1 + 列2" 或 "列1 * 2"
            try:
                # 使用eval安全地评估表达式
                # 将列名替换为df['列名']格式
                safe_expr = expression
                for col in df.columns:
                    # 确保替换完整的列名，避免部分替换
                    if col in safe_expr:
                        # 使用正则表达式确保只替换完整的列名
                        pattern = r'\b' + re.escape(col) + r'\b'
                        safe_col = f"df['{col}']"
                        safe_expr = re.sub(pattern, safe_col, safe_expr)
                
                # 打印表达式用于调试
                print(f"执行表达式: {safe_expr}")
                
                # 计算新列值
                result = eval(safe_expr)
                df[new_column_name] = result
                operation_desc = f"已创建新列 '{new_column_name}'，基于计算表达式: {expression}"
                
            except Exception as e:
                raise Exception(f"表达式计算错误: {str(e)}")
                
        elif expr_type == "条件":
            # 条件表达式，如 "列1 > 10"
            try:
                # 解析条件表达式
                condition = expression
                for col in df.columns:
                    # 确保替换完整的列名
                    if col in condition:
                        pattern = r'\b' + re.escape(col) + r'\b'
                        safe_col = f"df['{col}']"
                        condition = re.sub(pattern, safe_col, condition)
                
                # 打印条件用于调试
                print(f"执行条件: {condition}")
                
                # 创建新列基于条件
                df[new_column_name] = np.where(eval(condition), 1, 0)
                operation_desc = f"已创建新列 '{new_column_name}'，基于条件: {expression}"
                
            except Exception as e:
                raise Exception(f"条件表达式错误: {str(e)}")
                
        elif expr_type == "连接":
            # 连接多列，如 "列1 + '-' + 列2"
            try:
                # 检查表达式格式
                if "+" not in expression:
                    raise ValueError("连接表达式必须使用+号连接不同部分，例如: '列1 + \"-\" + 列2'")
                
                # 解析连接表达式
                parts = expression.split('+')
                result_series = None
                
                # 评估每一部分
                for part in parts:
                    part = part.strip()
                    
                    if part.startswith("'") and part.endswith("'"):
                        # 字符串常量
                        part_value = part[1:-1]  # 去除引号
                        part_series = pd.Series([part_value] * len(df))
                    elif part.startswith('"') and part.endswith('"'):
                        # 双引号字符串常量
                        part_value = part[1:-1]  # 去除引号
                        part_series = pd.Series([part_value] * len(df))
                    elif part in df.columns:
                        # 列名
                        part_series = df[part].astype(str)
                    else:
                        raise ValueError(f"无效的表达式部分: {part}，必须是带引号的字符串或有效的列名")
                    
                    # 累加结果
                    if result_series is None:
                        result_series = part_series
                    else:
                        result_series = result_series + part_series
                
                df[new_column_name] = result_series
                operation_desc = f"已创建新列 '{new_column_name}'，基于连接表达式: {expression}"
                
            except Exception as e:
                raise Exception(f"连接表达式错误: {str(e)}")
        else:
            raise ValueError(f"不支持的表达式类型: {expr_type}")
        
        return df, operation_desc
    
    def _apply_operator(self, df, column, operator, value):
        """应用操作符生成布尔掩码"""
        if operator == "==":
            return df[column] == value
        elif operator == ">":
            return df[column] > value
        elif operator == "<":
            return df[column] < value
        elif operator == ">=":
            return df[column] >= value
        elif operator == "<=":
            return df[column] <= value
        elif operator == "!=":
            return df[column] != value
        elif operator == "包含":
            # 对于字符串类型，检查是否包含子字符串
            return df[column].astype(str).str.contains(str(value), na=False)
        elif operator == "开始于":
            return df[column].astype(str).str.startswith(str(value), na=False)
        elif operator == "结束于":
            return df[column].astype(str).str.endswith(str(value), na=False)
        else:
            raise ValueError(f"不支持的操作符: {operator}")
    
    def _create_pivot_table(self, index, columns, values, aggregation):
        """创建数据透视表"""
        if not index or not values:
            raise ValueError("索引列和数值列必须指定")
        
        df = self.current_excel_df.copy()
        
        # 设置聚合函数的中英文映射
        agg_funcs = {
            "mean": "平均值",
            "sum": "总和",
            "count": "计数",
            "min": "最小值",
            "max": "最大值",
            "first": "第一个值",
            "last": "最后一个值"
        }
        
        operation_desc = ""
        
        try:
            # 创建数据透视表
            if columns:  # 如果指定了列标签
                pivot = pd.pivot_table(
                    df, 
                    values=values, 
                    index=index, 
                    columns=columns, 
                    aggfunc=aggregation,
                    fill_value=0
                )
                operation_desc = f"已创建数据透视表 - 索引:{index}, 列:{columns}, 值:{values}, 聚合:{agg_funcs.get(aggregation, aggregation)}"
            else:  # 如果没有指定列标签
                pivot = pd.pivot_table(
                    df, 
                    values=values, 
                    index=index, 
                    aggfunc=aggregation,
                    fill_value=0
                )
                operation_desc = f"已创建数据透视表 - 索引:{index}, 值:{values}, 聚合:{agg_funcs.get(aggregation, aggregation)}"
            
            # 重置索引，将索引转换为列
            result = pivot.reset_index()
            
            # 查看结果是否为空
            if result.empty:
                raise ValueError("生成的数据透视表为空，请检查数据或更换聚合函数")
                
            return result, operation_desc
            
        except Exception as e:
            # 对于count操作，尝试使用groupby而不是pivot_table
            if aggregation == "count":
                try:
                    # 使用groupby实现计数
                    if columns:
                        # 使用groupby和crosstab结合实现多维计数
                        result = pd.crosstab(
                            index=df[index], 
                            columns=df[columns],
                            values=df[values] if values in df else None,
                            aggfunc='count'
                        ).reset_index()
                        operation_desc = f"已创建计数表 - 按'{index}'和'{columns}'统计'{values}'的出现次数"
                    else:
                        # 简单的groupby计数
                        result = df.groupby(index)[values].count().reset_index()
                        result.columns = [index, f"{values}_计数"]
                        operation_desc = f"已创建计数表 - 按'{index}'统计'{values}'的出现次数"
                        
                    return result, operation_desc
                    
                except Exception as inner_e:
                    # 如果groupby方法也失败，则尝试最简单的值计数
                    try:
                        # 使用value_counts实现计数
                        counts = df[index].value_counts().reset_index()
                        counts.columns = [index, '计数']
                        operation_desc = f"已创建计数表 - 统计'{index}'的出现次数"
                        return counts, operation_desc
                    except:
                        # 所有方法都失败，抛出原始异常
                        raise e
            else:
                # 不是count操作，抛出原始异常
                raise
    
    def _sort_data(self, column, sort_order):
        """对数据进行排序"""
        if not column:
            raise ValueError("请选择排序列")
            
        df = self.current_excel_df.copy()
        
        # 确定排序方向
        is_ascending = (sort_order == "升序")
        
        # 执行排序
        result = df.sort_values(by=column, ascending=is_ascending)
        
        # 重置索引
        result = result.reset_index(drop=True)
        
        # 描述操作
        direction = "升序" if is_ascending else "降序"
        operation_desc = f"已按列 '{column}' {direction}排列数据"
        
        return result, operation_desc
    
    def _normalize_data(self, column, method):
        """对数值列进行归一化或标准化处理"""
        if not column:
            raise ValueError("请选择要处理的列")
            
        df = self.current_excel_df.copy()
        
        # 检查列是否为数值类型
        if not pd.api.types.is_numeric_dtype(df[column]):
            raise ValueError(f"列 '{column}' 不是数值类型，无法进行归一化处理")
        
        # 保存原列
        original_column = df[column].copy()
        
        # 根据不同方法进行归一化
        if method == "min-max":
            # Min-Max归一化 (0-1)
            min_val = original_column.min()
            max_val = original_column.max()
            
            if max_val == min_val:
                # 处理所有值相同的情况
                df[column] = 0.5
            else:
                df[column] = (original_column - min_val) / (max_val - min_val)
            
            operation_desc = f"已对列 '{column}' 进行Min-Max归一化 (0-1)"
            
        elif method == "z-score":
            # Z-score标准化 (均值为0，标准差为1)
            mean = original_column.mean()
            std = original_column.std()
            
            if std == 0:
                # 处理标准差为0的情况
                df[column] = 0
            else:
                df[column] = (original_column - mean) / std
            
            operation_desc = f"已对列 '{column}' 进行Z-score标准化"
            
        elif method == "robust":
            # 稳健归一化 (基于中位数和四分位差)
            median = original_column.median()
            q1 = original_column.quantile(0.25)
            q3 = original_column.quantile(0.75)
            iqr = q3 - q1
            
            if iqr == 0:
                # 处理IQR为0的情况
                df[column] = 0
            else:
                df[column] = (original_column - median) / iqr
            
            operation_desc = f"已对列 '{column}' 进行稳健归一化"
            
        else:
            raise ValueError(f"不支持的归一化方法: {method}")
        
        # 添加原始列，用"_原始"后缀
        df[f"{column}_原始"] = original_column
        
        return df, operation_desc
    
    def _apply_multi_filter(self, filter_conditions, logic_relation):
        """应用多条件筛选"""
        if not filter_conditions:
            raise ValueError("没有提供筛选条件")
        
        df = self.current_excel_df.copy()
        masks = []
        conditions_text = []
        
        # 生成每个条件的掩码
        for col, op, val in filter_conditions:
            # 对数值列进行适当的值转换
            try:
                if df[col].dtype.kind in 'ifc':
                    val = float(val)
            except (ValueError, TypeError):
                pass
            
            # 创建单个条件的掩码
            mask = self._apply_operator(df, col, op, val)
            masks.append(mask)
            conditions_text.append(f"{col} {op} {val}")
        
        # 组合所有条件
        if logic_relation == "AND":
            final_mask = masks[0]
            for mask in masks[1:]:
                final_mask = final_mask & mask
            logic_text = " AND "
        else:  # OR
            final_mask = masks[0]
            for mask in masks[1:]:
                final_mask = final_mask | mask
            logic_text = " OR "
        
        # 筛选数据
        result = df[final_mask]
        
        # 生成操作描述
        operation_desc = f"已使用多条件筛选: {logic_text.join(conditions_text)}"
        
        return result, operation_desc
    
    def _add_filter_condition(self):
        """添加一个新的筛选条件"""
        if self.multi_filter_frame is None or not self.multi_filter_frame.winfo_exists():
            return
        
        # 创建条件框架
        condition_frame = ttk.Frame(self.multi_filter_frame)
        condition_frame.pack(fill="x", pady=3)
        
        # 添加列选择
        col_combo = ttk.Combobox(condition_frame, width=20)
        if self.current_excel_df is not None:
            col_combo['values'] = list(self.current_excel_df.columns)
            if len(self.current_excel_df.columns) > 0:
                col_combo.set(self.current_excel_df.columns[0])
        col_combo.pack(side="left", padx=2)
        
        # 添加操作符选择
        op_combo = ttk.Combobox(condition_frame, width=8)
        op_combo['values'] = ["==", ">", "<", ">=", "<=", "!=", "包含", "开始于", "结束于"]
        op_combo.set("==")
        op_combo.pack(side="left", padx=2)
        
        # 添加值输入
        val_entry = ttk.Entry(condition_frame, width=15)
        val_entry.pack(side="left", padx=2, fill="x", expand=True)
        
        # 添加删除按钮
        del_btn = ttk.Button(condition_frame, text="×", width=3, 
                            command=lambda f=condition_frame: self._remove_filter_condition(f))
        del_btn.pack(side="left", padx=2)
        
        # 存储条件框架
        self.filter_conditions.append(condition_frame)
    
    def _remove_filter_condition(self, condition_frame):
        """移除一个筛选条件"""
        if len(self.filter_conditions) > 1:  # 确保至少保留一个条件
            if condition_frame in self.filter_conditions:
                self.filter_conditions.remove(condition_frame)
            condition_frame.destroy()
    
    def export_operation_config(self):
        """导出特殊操作配置到JSON文件"""
        if self.special_ops_window is None or not self.special_ops_window.winfo_exists():
            self.update_chat("请先打开特殊操作窗口")
            return
            
        # 获取当前操作类型和所有设置
        config = {}
        
        # 从界面获取当前设置
        for widget in self.special_ops_window.winfo_children():
            if isinstance(widget, ttk.LabelFrame) and widget['text'] == "选择操作":
                # 获取操作类型
                for child in widget.winfo_children():
                    if isinstance(child, ttk.Radiobutton):
                        if child.invoke() == "":  # 检查是否选中
                            config['op_type'] = child['value']
                            break
            
            elif isinstance(widget, ttk.LabelFrame) and widget['text'] == "操作条件":
                # 获取操作条件
                operation_params = {}
                
                for child in widget.winfo_children():
                    if isinstance(child, ttk.Label):
                        continue  # 跳过标签
                        
                    if isinstance(child, ttk.Combobox):
                        # 获取下拉框值
                        label_widget = None
                        # 查找对应的标签
                        for label in widget.winfo_children():
                            if isinstance(label, ttk.Label) and label.grid_info().get('row') == child.grid_info().get('row'):
                                label_widget = label
                                break
                                
                        if label_widget:
                            key = label_widget['text'].replace(':', '')
                            operation_params[key] = child.get()
                    
                    elif isinstance(child, ttk.Entry):
                        # 获取输入框值
                        label_widget = None
                        # 查找对应的标签
                        for label in widget.winfo_children():
                            if isinstance(label, ttk.Label) and label.grid_info().get('row') == child.grid_info().get('row'):
                                label_widget = label
                                break
                                
                        if label_widget:
                            key = label_widget['text'].replace(':', '')
                            operation_params[key] = child.get()
                
                # 获取多条件筛选的特殊设置
                if hasattr(self, 'multi_filter_frame') and self.multi_filter_frame is not None and self.multi_filter_frame.winfo_exists():
                    multi_conditions = []
                    for condition_frame in self.filter_conditions:
                        if condition_frame.winfo_exists():
                            col_combo = None
                            op_combo = None
                            val_entry = None
                            
                            for w in condition_frame.winfo_children():
                                if isinstance(w, ttk.Combobox):
                                    if col_combo is None:
                                        col_combo = w
                                    else:
                                        op_combo = w
                                elif isinstance(w, ttk.Entry):
                                    val_entry = w
                            
                            if col_combo and op_combo and val_entry:
                                multi_conditions.append({
                                    "column": col_combo.get(),
                                    "operator": op_combo.get(),
                                    "value": val_entry.get()
                                })
                    
                    operation_params['multi_conditions'] = multi_conditions
                    operation_params['logic_relation'] = self.logic_var.get()
                
                config['operation_params'] = operation_params
        
        # 如果没有获取到操作类型，使用默认值
        if 'op_type' not in config:
            config['op_type'] = "delete_rows"
            
        # 添加保存时间
        from datetime import datetime
        config['saved_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        config['description'] = "Excel AI特殊操作配置"
        
        # 创建配置名称和描述输入窗口
        config_info_window = tk.Toplevel(self.special_ops_window)
        config_info_window.title("配置信息")
        config_info_window.geometry("400x200")
        config_info_window.transient(self.special_ops_window)
        config_info_window.grab_set()  # 设为模态窗口
        
        # 名称输入
        name_frame = ttk.Frame(config_info_window)
        name_frame.pack(fill="x", padx=10, pady=5)
        
        name_label = ttk.Label(name_frame, text="配置名称:")
        name_label.pack(side="left", padx=5)
        
        name_var = tk.StringVar(value="特殊操作配置")
        name_entry = ttk.Entry(name_frame, textvariable=name_var, width=30)
        name_entry.pack(side="left", padx=5, fill="x", expand=True)
        
        # 描述输入
        desc_frame = ttk.Frame(config_info_window)
        desc_frame.pack(fill="both", padx=10, pady=5, expand=True)
        
        desc_label = ttk.Label(desc_frame, text="配置描述:")
        desc_label.pack(anchor="nw", padx=5, pady=5)
        
        desc_text = tk.Text(desc_frame, height=4, width=40)
        desc_text.pack(fill="both", padx=5, expand=True)
        desc_text.insert("1.0", "Excel AI特殊操作配置")
        
        # 按钮区域
        button_frame = ttk.Frame(config_info_window)
        button_frame.pack(fill="x", padx=10, pady=10)
        
        # 保存函数
        def save_config():
            config['name'] = name_var.get()
            config['description'] = desc_text.get("1.0", tk.END).strip()
            
            # 获取保存路径
            file_path = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON文件", "*.json")],
                initialfile=f"{config['name']}.json"
            )
            
            if file_path:
                try:
                    import json
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(config, f, ensure_ascii=False, indent=4)
                        
                    self.update_chat(f"已将操作配置'{config['name']}'导出到: {file_path}")
                except Exception as e:
                    self.update_chat(f"导出配置失败: {str(e)}")
                    traceback.print_exc()
            
            config_info_window.destroy()
        
        # 保存按钮
        save_btn = ttk.Button(button_frame, text="保存", command=save_config)
        save_btn.pack(side="left", padx=5)
        
        # 取消按钮
        cancel_btn = ttk.Button(button_frame, text="取消", command=config_info_window.destroy)
        cancel_btn.pack(side="right", padx=5)
    
    def import_operation_config(self):
        """从JSON文件导入特殊操作配置"""
        # 选择配置文件
        file_path = filedialog.askopenfilename(
            title="选择配置文件",
            filetypes=[("JSON文件", "*.json")]
        )
        
        if not file_path:
            self.update_chat("未选择配置文件")
            return
            
        self.update_chat(f"正在从文件导入配置: {file_path}")
            
        try:
            # 读取JSON文件
            import json
            with open(file_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 基本验证
            if not isinstance(config, dict):
                self.update_chat("配置文件格式无效: 不是有效的JSON对象")
                return
                
            if 'op_type' not in config:
                self.update_chat("配置文件格式无效: 缺少'op_type'字段")
                return
                
            if 'operation_params' not in config:
                self.update_chat("配置文件格式无效: 缺少'operation_params'字段")
                return
                
            # 获取操作类型
            op_type = config['op_type']
            self.update_chat(f"配置操作类型: {op_type}")
            
            # 关闭当前特殊操作窗口
            if self.special_ops_window and self.special_ops_window.winfo_exists():
                self.special_ops_window.destroy()
                self.update_chat("已关闭当前特殊操作窗口")
            
            # 打开新的特殊操作窗口
            self.show_special_operations()
            self.update_chat("已打开新的特殊操作窗口")
            
            # 等待一下确保窗口完全加载
            self.root.update()
            self.root.after(300)  # 延迟300毫秒，确保界面完全更新
            
            if not self.special_ops_window or not self.special_ops_window.winfo_exists():
                self.update_chat("特殊操作窗口创建失败")
                return
                
            # 设置操作类型
            self.update_chat(f"正在设置操作类型: {op_type}")
            
            # 查找操作类型单选按钮
            op_set = False
            for widget in self.special_ops_window.winfo_children():
                if isinstance(widget, ttk.LabelFrame) and widget['text'] == "选择操作":
                    for child in widget.winfo_children():
                        if isinstance(child, ttk.Radiobutton) and child['value'] == op_type:
                            child.invoke()  # 选中对应的单选按钮
                            op_set = True
                            self.update_chat(f"已选择操作类型: {op_type}")
                            break
            
            if not op_set:
                self.update_chat(f"未找到对应的操作类型: {op_type}")
            
            # 等待操作类型设置生效
            self.root.update()
            self.root.after(300)  # 再次延迟300毫秒
            
            # 设置操作参数
            if 'operation_params' in config:
                params = config['operation_params']
                self.update_chat(f"发现参数: {list(params.keys())}")
                
                # 处理多条件筛选的特殊情况
                if op_type == "multi_filter" and 'multi_conditions' in params:
                    self.update_chat("处理多条件筛选配置")
                    
                    # 设置逻辑关系
                    if 'logic_relation' in params:
                        self.logic_var.set(params['logic_relation'])
                        self.update_chat(f"设置逻辑关系: {params['logic_relation']}")
                    
                    # 清除现有条件
                    if hasattr(self, 'filter_conditions'):
                        for frame in self.filter_conditions:
                            if frame.winfo_exists():
                                frame.destroy()
                        self.filter_conditions = []
                        self.update_chat("已清除现有条件")
                    
                    # 添加导入的条件
                    if 'multi_conditions' in params:
                        conditions = params['multi_conditions']
                        self.update_chat(f"发现{len(conditions)}个条件")
                        
                        for i, condition in enumerate(conditions):
                            self.update_chat(f"添加条件 {i+1}/{len(conditions)}")
                            
                            # 确保multi_filter_frame已创建
                            if self.multi_filter_frame is None or not self.multi_filter_frame.winfo_exists():
                                self.update_chat("多条件框架不存在，尝试创建")
                                self._add_filter_condition()
                                continue
                                
                            # 添加新条件
                            self._add_filter_condition()
                            
                            # 设置条件值
                            if self.filter_conditions and len(self.filter_conditions) > 0:
                                condition_frame = self.filter_conditions[-1]
                                
                                col_combo = None
                                op_combo = None
                                val_entry = None
                                
                                for w in condition_frame.winfo_children():
                                    if isinstance(w, ttk.Combobox):
                                        if col_combo is None:
                                            col_combo = w
                                        else:
                                            op_combo = w
                                    elif isinstance(w, ttk.Entry):
                                        val_entry = w
                                
                                if col_combo and 'column' in condition:
                                    col_combo.set(condition['column'])
                                    self.update_chat(f"设置列: {condition['column']}")
                                    
                                if op_combo and 'operator' in condition:
                                    op_combo.set(condition['operator'])
                                    self.update_chat(f"设置操作符: {condition['operator']}")
                                    
                                if val_entry and 'value' in condition:
                                    val_entry.delete(0, tk.END)
                                    val_entry.insert(0, condition['value'])
                                    self.update_chat(f"设置值: {condition['value']}")
                else:
                    # 常规参数设置
                    self.update_chat("设置常规参数")
                    params_set = []
                    
                    # 进行多次搜索，因为界面可能随操作类型选择而变化
                    # 等待界面更新完成
                    self.root.update()
                    self.root.after(500)  # 延迟更长时间(500ms)确保界面已经完全更新
                    
                    # 获取所有可见的标签及其对应行
                    available_labels = []
                    input_controls = {}
                    
                    # 先获取所有可操作的控件
                    for widget in self.special_ops_window.winfo_children():
                        if isinstance(widget, ttk.LabelFrame) and widget['text'] == "操作条件":
                            # 收集所有标签及其行号
                            for child in widget.winfo_children():
                                if isinstance(child, ttk.Label):
                                    try:
                                        row = child.grid_info().get('row')
                                        if row is not None:
                                            label_text = child['text'].replace(':', '')
                                            available_labels.append((label_text, row))
                                            self.update_chat(f"找到控件标签: {label_text}, 行: {row}")
                                    except:
                                        pass
                                        
                            # 收集所有输入控件和行号        
                            for child in widget.winfo_children():
                                if isinstance(child, ttk.Combobox) or isinstance(child, ttk.Entry):
                                    try:
                                        row = child.grid_info().get('row')
                                        if row is not None:
                                            if row not in input_controls:
                                                input_controls[row] = []
                                            input_controls[row].append(child)
                                    except:
                                        pass
                    
                    # 遍历配置参数，应用到对应控件
                    for param_name, param_value in params.items():
                        if param_name in ['multi_conditions', 'logic_relation']:
                            continue
                            
                        self.update_chat(f"尝试设置参数: {param_name} = {param_value}")
                        
                        # 查找对应标签的行
                        matching_rows = []
                        for label_text, row in available_labels:
                            if label_text == param_name:
                                matching_rows.append(row)
                                self.update_chat(f"找到匹配标签: {label_text}, 行: {row}")
                        
                        # 对每个匹配行设置控件值
                        for row in matching_rows:
                            if row in input_controls:
                                for control in input_controls[row]:
                                    try:
                                        if isinstance(control, ttk.Combobox):
                                            control.set(param_value)
                                            self.update_chat(f"设置下拉框值: {param_value}")
                                            params_set.append(param_name)
                                        elif isinstance(control, ttk.Entry):
                                            control.delete(0, tk.END)
                                            control.insert(0, param_value)
                                            self.update_chat(f"设置输入框值: {param_value}")
                                            params_set.append(param_name)
                                    except Exception as e:
                                        self.update_chat(f"设置控件值失败: {str(e)}")
                    
                    # 检查是否所有参数都设置了
                    for param_name in params:
                        if param_name not in params_set and param_name not in ['multi_conditions', 'logic_relation']:
                            self.update_chat(f"警告: 参数 '{param_name}' 未能设置，可能控件不存在或已被隐藏")
            
            # 读取配置信息
            config_name = config.get('name', '特殊操作配置')
            config_description = config.get('description', '')
            saved_time = config.get('saved_time', '未知时间')
            
            self.update_chat(f"已导入配置: {config_name}")
            
            if config_description:
                self.update_chat(f"配置描述: {config_description}")
                
            self.update_chat(f"配置保存时间: {saved_time}")
            
            # 在特殊操作窗口标题中显示配置名称
            if self.special_ops_window and self.special_ops_window.winfo_exists():
                self.special_ops_window.title(f"特殊数据操作 - {config_name}")
                self.update_chat("已更新窗口标题")
            
            # 尝试预览操作 - 延迟更长时间
            self.root.after(1000, lambda: self._try_preview_imported_config(op_type))
            
        except Exception as e:
            self.update_chat(f"导入配置失败: {str(e)}")
            traceback.print_exc()
    
    def _try_preview_imported_config(self, op_type):
        """尝试预览导入的配置"""
        try:
            self.update_chat("尝试预览导入的配置...")
            
            # 获取预览区域 - 先尝试直接遍历窗口的子元素
            preview_text = None
            
            # 检查所有窗口组件
            for widget in self.special_ops_window.winfo_children():
                self.update_chat(f"检查窗口组件: {widget}")
                if isinstance(widget, ttk.LabelFrame) and widget['text'] == "数据预览":
                    for child in widget.winfo_children():
                        if isinstance(child, scrolledtext.ScrolledText):
                            preview_text = child
                            self.update_chat("找到预览文本区域")
                            break
            
            # 如果上面的方法没找到，尝试更通用的查找方法
            if not preview_text:
                self.update_chat("使用备用方法查找预览区域...")
                for widget in self.special_ops_window.winfo_children():
                    # 检查是否是LabelFrame
                    if hasattr(widget, 'cget') and widget.cget('text') == "数据预览":
                        # 在LabelFrame里查找ScrolledText
                        for child in widget.winfo_children():
                            if isinstance(child, scrolledtext.ScrolledText) or hasattr(child, 'insert'):
                                preview_text = child
                                self.update_chat("找到预览文本区域(备用方法)")
                                break
            
            if not preview_text:
                self.update_chat("未能找到预览文本区域，无法执行预览")
                return
            
            # 获取各个参数
            col_var = tk.StringVar()
            op_var = tk.StringVar()
            val_var = tk.StringVar()
            new_val_var = tk.StringVar()
            index_var = tk.StringVar()
            values_var = tk.StringVar() 
            agg_var = tk.StringVar()
            sort_order_var = tk.StringVar()
            normalize_method_var = tk.StringVar()
            
            # 收集当前控件中的值
            self.update_chat("收集参数值...")
            
            # 收集所有参数
            for widget in self.special_ops_window.winfo_children():
                if isinstance(widget, ttk.LabelFrame) and widget['text'] == "操作条件":
                    # 直接获取所有输入控件的值
                    for child in widget.winfo_children():
                        if isinstance(child, ttk.Combobox) or isinstance(child, ttk.Entry):
                            try:
                                row = child.grid_info().get('row')
                                value = child.get() if hasattr(child, 'get') else ""
                                
                                # 查找对应的标签
                                for lbl in widget.winfo_children():
                                    if isinstance(lbl, ttk.Label) and lbl.grid_info().get('row') == row:
                                        label_text = lbl['text'].replace(':', '')
                                        self.update_chat(f"收集参数: {label_text} = {value}")
                                        
                                        # 根据标签文本设置变量
                                        if '选择列' in label_text:
                                            col_var.set(value)
                                        elif '操作符' in label_text:
                                            op_var.set(value)
                                        elif label_text == '值':
                                            val_var.set(value)
                                        elif '新值' in label_text or label_text == '新值':
                                            new_val_var.set(value)
                                        elif '索引列' in label_text:
                                            index_var.set(value)
                                        elif '数值列' in label_text:
                                            values_var.set(value)
                                        elif '聚合函数' in label_text:
                                            agg_var.set(value)
                                        elif '排序方式' in label_text:
                                            sort_order_var.set(value)
                                        elif '归一化方法' in label_text:
                                            normalize_method_var.set(value)
                                        break
                            except Exception as e:
                                self.update_chat(f"获取参数值时出错: {str(e)}")
            
            # 检查是否有足够的参数执行预览
            if op_type == "delete_rows" and (not col_var.get() or not op_var.get()):
                self.update_chat(f"预览所需参数不足: 选择列={col_var.get()}, 操作符={op_var.get()}, 值={val_var.get()}")
                return
                
            # 执行预览
            self.update_chat("执行预览...")
            self.update_chat(f"预览参数: 操作类型={op_type}, 列={col_var.get()}, 操作符={op_var.get()}, 值={val_var.get()}")
            
            # 清空预览区域
            preview_text.delete("1.0", tk.END)
            
            try:
                self._preview_operation(
                    op_type,
                    col_var.get(),
                    op_var.get(),
                    val_var.get(),
                    new_val_var.get(),
                    index_var.get(),
                    values_var.get(),
                    agg_var.get(),
                    sort_order_var.get(),
                    normalize_method_var.get(),
                    preview_text
                )
                self.update_chat("预览操作完成")
            except Exception as e:
                self.update_chat(f"执行预览操作失败: {str(e)}")
                traceback.print_exc()
            
        except Exception as e:
            self.update_chat(f"预览导入的配置失败: {str(e)}")
            traceback.print_exc()


# 主程序
if __name__ == "__main__":
    root = tk.Tk()
    root.geometry("900x700")
    app = ExcelAIApp(root)
    root.mainloop()

