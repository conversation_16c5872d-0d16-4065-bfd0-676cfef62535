import requests
import json
import os
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np
import tkinter as tk
from tkinter import ttk, filedialog, scrolledtext
import matplotlib
from pandasai import Agent
from pandasai.llm import LLM

# 设置matplotlib支持中文
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 用黑体显示中文
matplotlib.rcParams['axes.unicode_minus'] = False    # 正常显示负号

class OllamaAPI:
    """与Ollama API交互的类"""
    
    def __init__(self, base_url="http://localhost:11434/api"):
        self.base_url = base_url
        
    def get_available_models(self):
        """获取可用模型列表"""
        tags_url = f"{self.base_url}/tags"
        try:
            response = requests.get(tags_url)
            if response.status_code == 200:
                result = response.json()
                if 'models' in result and isinstance(result['models'], list):
                    models = [model.get('name') for model in result['models'] if 'name' in model]
                    return models
                else:
                    return []
            else:
                return []
        except requests.exceptions.RequestException as e:
            print(f"获取模型列表失败: {str(e)}")
            return []
    
    def chat(self, model_name, messages):
        """与模型对话"""
        chat_url = f"{self.base_url}/chat"
        headers = {"Content-Type": "application/json"}
        
        payload = {
            "model": model_name,
            "messages": messages,
            "stream": False
        }
        
        try:
            response = requests.post(chat_url, headers=headers, data=json.dumps(payload))
            
            if response.status_code == 200:
                result = response.json()
                if 'message' in result and 'content' in result['message']:
                    return result['message']['content']
                elif 'choices' in result and len(result['choices']) > 0 and 'message' in result['choices'][0]:
                    return result['choices'][0]['message']['content']
                elif 'response' in result:
                    return result['response']
                else:
                    return f"响应格式未知: {json.dumps(result)}"
            else:
                return f"请求失败，状态码: {response.status_code}, 错误信息: {response.text}"
        
        except requests.exceptions.RequestException as e:
            return f"网络错误: {str(e)}"
        except Exception as e:
            return f"其他错误: {str(e)}"


class OllamaLLM(LLM):
    """自定义LLM类用于连接Ollama，适配PandasAI"""
    
    def __init__(self, model_name="llama3", api_base="http://localhost:11434/api"):
        self.model = model_name
        self.api_base = api_base
        self.ollama_api = OllamaAPI(api_base)
        
    def generate_code(self, instruction, context=None):
        """生成代码，覆盖父类方法"""
        # 构建提示
        prompt = f"""
{instruction}

请生成Python代码来回答这个问题。确保代码可以直接运行，并将结果存储在名为'result'的变量中。
结果必须是一个包含'type'和'value'键的字典，例如：
- 对于DataFrame结果：result = {{"type": "dataframe", "value": df_result}}
- 对于字符串结果：result = {{"type": "string", "value": "结果文本"}}
- 对于图表结果：result = {{"type": "plot", "value": fig}}

代码必须放在```python和```标记之间。

例如：

```python
import pandas as pd
import matplotlib.pyplot as plt

# 分析数据
stats = df.describe()
result = {{"type": "dataframe", "value": stats}}
```

注意：
1. 确保检查df是否为None
2. 使用try-except块处理可能的错误
3. 返回有意义的结果，必须是包含type和value的字典
"""
        
        # 调用LLM生成代码
        response = self.call(prompt, context if context else "")
        
        # 尝试从响应中提取代码
        try:
            # 尝试查找```python和```之间的代码
            if "```python" in response:
                code_blocks = response.split("```python")
                if len(code_blocks) > 1:
                    code_block = code_blocks[1].split("```")[0].strip()
                    if code_block:
                        return code_block
                        
            # 尝试查找```和```之间的代码
            if "```" in response:
                code_blocks = response.split("```")
                if len(code_blocks) > 2:  # 至少有一对```
                    code_block = code_blocks[1].strip()
                    if code_block.startswith("python"):
                        code_block = code_block[6:].strip()
                    if code_block:
                        return code_block
        except Exception:
            pass
            
        # 如果没有找到代码，生成一个基本的代码
        return """
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# 安全检查
if df is None:
    result = {"type": "string", "value": "数据框为None，请先加载有效的Excel文件"}
else:
    try:
        # 生成基本统计信息
        stats = df.describe(include='all').T
        result = {"type": "dataframe", "value": stats}
    except Exception as e:
        result = {"type": "string", "value": f"分析出错: {str(e)}"}
"""
        
    def call(self, instruction, value, suffix=""):
        """调用Ollama API处理请求"""
        url = f"{self.api_base}/chat"
        headers = {"Content-Type": "application/json"}
        
        # 处理value参数，可能是字符串或PipelineContext对象
        if hasattr(value, 'to_string'):
            content = value.to_string() + suffix
        elif hasattr(value, 'input') and hasattr(value, 'config'):
            # 处理PipelineContext对象
            if hasattr(value.input, 'to_string'):
                content = value.input.to_string() + suffix
            else:
                content = str(value.input) + suffix
        elif hasattr(value, '__str__'):
            content = str(value) + suffix
        else:
            content = str(value) + suffix if value is not None else suffix
        
        # 为PandasAI添加代码生成提示
        if isinstance(instruction, str) and ("generate code" in instruction.lower() or "python code" in instruction.lower()):
            instruction += """
请确保返回完整的Python代码，并将代码放在```python和```标记之间。
"""
        
        # 创建标准payload
        payload = {
            "model": self.model,
            "messages": [
                {"role": "system", "content": instruction},
                {"role": "user", "content": content}
            ],
            "stream": False
        }
        
        try:
            response = requests.post(url, headers=headers, json=payload)
            response.raise_for_status()
            result = response.json()
            
            if 'message' in result and 'content' in result['message']:
                return result['message']['content']
            elif 'response' in result:
                return result['response']
            else:
                return str(result)
                
        except Exception as e:
            return f"调用Ollama API出错: {str(e)}"
            
    @property
    def type(self):
        return "ollama"


class ExcelProcessor:
    """Excel文件处理类"""
    
    @staticmethod
    def load_excel(file_path):
        """加载Excel文件并返回基本信息"""
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                return {
                    "success": False,
                    "dataframe": None,
                    "info": None,
                    "message": f"错误: 文件不存在: '{file_path}'"
                }
                
            # 检查文件扩展名是否为Excel
            extension = os.path.splitext(file_path)[1].lower()
            if extension not in ['.xlsx', '.xls']:
                return {
                    "success": False,
                    "dataframe": None,
                    "info": None,
                    "message": f"错误: 不支持的文件格式: '{extension}'，只支持.xlsx和.xls"
                }
                
            # 读取Excel文件
            df = pd.read_excel(file_path)
            
            # 返回数据框的基本信息
            info = {
                "columns": list(df.columns),
                "shape": df.shape,
                "dtypes": {col: str(dtype) for col, dtype in df.dtypes.items()},
                "head": df.head(5).to_dict('records'),
                "describe": df.describe().to_dict() if len(df.select_dtypes(include=['number']).columns) > 0 else None
            }
            
            return {
                "success": True,
                "dataframe": df,
                "info": info,
                "message": "Excel文件已成功加载，可以使用自然语言进行查询"
            }
        except Exception as e:
            return {
                "success": False,
                "dataframe": None,
                "info": None,
                "message": f"处理Excel文件时出错: {str(e)}"
            }


class PandasAIHelper:
    """PandasAI助手类"""
    
    def __init__(self, model_name="llama3", api_base="http://localhost:11434/api"):
        self.model_name = model_name
        self.api_base = api_base
        self.agent = None
        
    def initialize(self, df=None):
        """初始化PandasAI Agent"""
        try:
            # 创建自定义Ollama LLM实例
            llm = OllamaLLM(
                model_name=self.model_name,
                api_base=self.api_base
            )
            # 创建PandasAI Agent实例
            self.agent = Agent(
                [df] if df is not None else [],  # 传入数据框列表，如果有的话
                config={"llm": llm}  # 传入LLM实例
            )
            return True
        except Exception as e:
            print(f"初始化PandasAI失败: {str(e)}")
            return False
    
    def process_query(self, df, query):
        """处理自然语言查询(根据PandasAI官方推荐方式)"""
        # 检查输入DataFrame
        if df is None:
            return None, "数据框为None，请先加载有效的Excel文件"
            
        # 确保Agent已初始化且包含当前DataFrame
        if self.agent is None or not hasattr(self.agent, 'dfs') or len(self.agent.dfs) == 0:
            if not self.initialize(df):
                return None, "初始化PandasAI失败"
        
        try:
            # 根据官方建议格式化查询
            formatted_query = f"""
请对以下数据执行操作:
{df.head(3).to_string()}

具体要求:
{query}

请:
1. 如果是删除行请求，使用df = df[df['列名'] != '值']
2. 如果是分析请求，返回统计结果
3. 如果是可视化请求，返回图表
4. 如果是筛选请求，返回新DataFrame

示例(删除姓名=甲的行):
```python
df = df[df['姓名'] != '甲']
result = {"type": "dataframe", "value": df}
```
"""
            # 确保agent和chat方法可用
            if self.agent is None or not hasattr(self.agent, 'chat'):
                return None, "PandasAI代理未正确初始化"
                
            result = self.agent.chat(formatted_query)
            
            # 官方推荐的结果处理方式
            if isinstance(result, str):
                return result, None
            elif isinstance(result, (pd.DataFrame, pd.Series)):
                return result, None
            elif hasattr(result, 'figure') or isinstance(result, plt.figure.Figure):
                return result.figure if hasattr(result, 'figure') else result, None
            elif hasattr(result, '_repr_html_'):
                # 处理可能有HTML表示的对象
                return str(result), None
            else:
                # 尝试转换为DataFrame
                try:
                    return pd.DataFrame(result), None
                except Exception:
                    return str(result), None
                
        except Exception as e:
            return None, f"处理出错(官方推荐处理方式): {str(e)}\n建议检查查询格式是否符合: [操作] [列名] [条件]"


class ExcelAIApp:
    """Excel AI应用主类"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("Excel AI助手 - 基于PandasAI")
        
        # 初始化API
        self.ollama_api = OllamaAPI()
        
        # 初始化变量
        self.model_name = tk.StringVar(value="llama3")
        self.conversation_history = []
        self.current_excel_df = None
        self.current_excel_path = None
        self.current_figure = None
        self.canvas = None
        self.pandasai_helper = None
        
        # 创建界面
        self.create_widgets()
        self.load_models()
    
    def create_widgets(self):
        """创建GUI界面元素"""
        # 模型选择区域
        model_frame = ttk.LabelFrame(self.root, text="模型选择")
        model_frame.pack(padx=10, pady=5, fill="x")
        
        self.model_combobox = ttk.Combobox(model_frame, textvariable=self.model_name, state="readonly")
        self.model_combobox.pack(padx=5, pady=5, fill="x")
        
        # 对话区域
        chat_frame = ttk.LabelFrame(self.root, text="对话")
        chat_frame.pack(padx=10, pady=5, expand=True, fill="both")
        
        self.chat_display = scrolledtext.ScrolledText(chat_frame, wrap=tk.WORD, state="disabled")
        self.chat_display.pack(padx=5, pady=5, expand=True, fill="both")
        
        # 输入区域
        input_frame = ttk.Frame(self.root)
        input_frame.pack(padx=10, pady=5, fill="x")
        
        # 使用Text控件替代Entry，增大输入框高度
        self.user_input = tk.Text(input_frame, height=4, wrap=tk.WORD)
        self.user_input.pack(padx=5, pady=5, fill="x")
        self.user_input.bind("<Return>", self.send_message)
        
        # 按钮区域
        button_frame = ttk.Frame(input_frame)
        button_frame.pack(fill="x")
        
        send_btn = ttk.Button(button_frame, text="发送", command=self.send_message)
        send_btn.pack(side="left", padx=5, pady=5)
        
        excel_btn = ttk.Button(button_frame, text="加载Excel", command=self.load_excel)
        excel_btn.pack(side="left", padx=5, pady=5)
        
        translate_btn = ttk.Button(button_frame, text="翻译", command=self.translate_text)
        translate_btn.pack(side="left", padx=5, pady=5)
        
        clear_btn = ttk.Button(button_frame, text="清空记录", command=self.clear_history)
        clear_btn.pack(side="right", padx=5, pady=5)
        
        save_btn = ttk.Button(button_frame, text="保存历史", command=self.save_history)
        save_btn.pack(side="right", padx=5, pady=5)
        
        # 可视化区域（初始隐藏）
        self.viz_frame = ttk.LabelFrame(self.root, text="数据可视化")
        # 不立即显示，等待有数据时再显示
    
    def load_models(self):
        """加载可用模型列表"""
        available_models = self.ollama_api.get_available_models()
        if available_models:
            self.model_combobox["values"] = available_models
            self.model_name.set(available_models[0])
        else:
            self.model_name.set("llama3")
            self.update_chat("无法获取模型列表，默认使用 'llama3'")
    
    def send_message(self, event=None):
        """发送用户消息"""
        user_input = self.user_input.get("1.0", tk.END).strip()
        if not user_input:
            return
            
        self.user_input.delete("1.0", tk.END)
        self.update_chat(f"您: {user_input}")
        
        # 检查是否是Excel查询
        if self.current_excel_df is not None:
            # 使用PandasAI处理Excel查询
            self.handle_excel_query(user_input)
        else:
            # 正常对话流程
            self.conversation_history.append({"role": "user", "content": user_input})
            response_text = self.ollama_api.chat(self.model_name.get(), self.conversation_history)
            self.update_chat(f"模型: {response_text}")
            self.conversation_history.append({"role": "assistant", "content": response_text})
    
    def load_excel(self):
        """加载Excel文件并初始化PandasAI"""
        # 选择Excel文件
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls")]
        )
        if not file_path:
            return
            
        self.update_chat(f"已选择Excel文件: {file_path}")
        self.current_excel_path = file_path
        
        # 使用ExcelProcessor加载Excel文件
        result = ExcelProcessor.load_excel(file_path)
        
        if result["success"]:
            self.current_excel_df = result["dataframe"]
            
            # 初始化PandasAI助手
            self.pandasai_helper = PandasAIHelper(model_name=self.model_name.get())
            if not self.pandasai_helper.initialize(self.current_excel_df):
                self.update_chat("初始化PandasAI失败，请检查模型配置")
                return
                
            # 显示基本信息
            info = result["info"]
            columns_str = ", ".join(str(col) for col in info["columns"])
            self.update_chat(f"Excel文件已加载，共{info['shape'][0]}行，{info['shape'][1]}列")
            self.update_chat(f"列名: {columns_str}")
            
            # 显示前5行数据预览
            preview = self.current_excel_df.head(5).to_string()
            self.update_chat(f"数据预览:\n{preview}")
            
            # 提示用户输入查询
            self.update_chat("\n现在您可以使用自然语言查询Excel数据，例如:")
            self.update_chat("- 计算销售额的平均值和总和")
            self.update_chat("- 按地区对数据进行分组并计算每组的统计数据")
            self.update_chat("- 创建一个销售额随时间变化的折线图")
            self.update_chat("- 筛选出销售额大于1000的记录")
            
        else:
            self.update_chat(f"加载Excel文件失败: {result['message']}")
    
    
    def handle_excel_query(self, query):
        """使用PandasAI处理Excel查询"""
        if self.current_excel_df is None or self.pandasai_helper is None:
            self.update_chat("错误: 没有加载Excel文件或PandasAI未初始化")
            return
            
        self.update_chat("正在处理您的查询...")
        
        # 直接处理查询
        self.process_query_directly(query)
    
    def process_query_directly(self, query):
        """直接处理查询，不显示代码编辑器"""
        # 检查PandasAI助手是否初始化
        if self.pandasai_helper is None:
            self.update_chat("错误: PandasAI未初始化")
            return
            
        # 使用PandasAI处理查询
        result, error = self.pandasai_helper.process_query(self.current_excel_df, query)
        
        if error:
            self.update_chat(error)
            self.fallback_to_chat(query)
            return
            
        # 检查结果是否为None
        if result is None:
            self.update_chat("查询结果: 无法获取结果，请尝试其他查询")
            return
            
        # 检查结果类型
        if isinstance(result, pd.DataFrame):
            # 如果结果是DataFrame，显示前10行
            self.update_chat("查询结果:")
            preview = result.head(10).to_string()
            self.update_chat(preview)
            
            # 更新当前DataFrame为查询结果
            self.current_excel_df = result
            self.add_save_result_button()
            
        elif isinstance(result, str):
            # 如果结果是字符串，直接显示
            self.update_chat(f"查询结果: {result}")
            
        elif hasattr(result, '_repr_html_') or isinstance(result, plt.figure.Figure):
            # 如果结果有HTML表示（如图表）
            self.update_chat("生成了可视化结果")
            self.display_visualization_result(result)
            
        else:
            # 其他类型的结果
            self.update_chat(f"查询结果: {str(result)}")
    
    def fallback_to_chat(self, query):
        """当PandasAI处理失败时，使用常规对话模型作为备选"""
        self.update_chat("尝试使用常规对话模型处理...")
        
        # 确保DataFrame不为None
        if self.current_excel_df is None:
            self.update_chat("错误: 没有可用的Excel数据")
            return
        
        # 构建提示信息
        df_info = {
            "columns": list(self.current_excel_df.columns),
            "shape": self.current_excel_df.shape,
            "head": self.current_excel_df.head(5).to_dict('records')
        }
        
        prompt = f"""
我有一个Excel数据集，具有以下特征:
- 行数: {df_info['shape'][0]}
- 列数: {df_info['shape'][1]}
- 列名: {', '.join(df_info['columns'])}
- 前5行数据: {json.dumps(df_info['head'], ensure_ascii=False, indent=2)}

用户的查询是: "{query}"

请提供详细的pandas代码来回答这个查询，并解释代码的作用。
"""
        
        # 添加到对话历史
        self.conversation_history.append({"role": "user", "content": prompt})
        
        # 获取模型响应
        response_text = self.ollama_api.chat(self.model_name.get(), self.conversation_history)
        self.update_chat(f"模型建议: {response_text}")
        self.conversation_history.append({"role": "assistant", "content": response_text})
    
    def display_visualization_result(self, result):
        """显示可视化结果"""
        try:
            # 检查结果是否为None
            if result is None:
                self.update_chat("错误: 无法显示可视化结果，结果为None")
                return
                
            # 检查是否是matplotlib图表
            if hasattr(result, 'figure') or isinstance(result, plt.figure.Figure):
                fig = result.figure if hasattr(result, 'figure') else result
                self.display_visualization(fig)
            else:
                # 尝试将结果转换为字符串并显示
                self.update_chat(str(result))
        except Exception as e:
            self.update_chat(f"显示结果时出错: {str(e)}")
    
    def display_visualization(self, fig):
        """在GUI中显示可视化图表"""
        # 清除之前的可视化
        if self.canvas:
            self.canvas.get_tk_widget().destroy()
        
        # 显示可视化区域
        if not self.viz_frame.winfo_ismapped():
            self.viz_frame.pack(padx=10, pady=5, fill="both", expand=True)
        
        # 创建新的画布
        self.canvas = FigureCanvasTkAgg(fig, master=self.viz_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill="both", expand=True)
        
        # 保存当前图表
        self.current_figure = fig
    
    def add_save_result_button(self):
        """添加保存结果按钮（如果不存在）"""
        # 检查按钮是否已存在
        for widget in self.root.winfo_children():
            if isinstance(widget, ttk.Frame) and hasattr(widget, 'save_result_btn'):
                return  # 按钮已存在
        
        # 创建保存结果按钮框架
        save_frame = ttk.Frame(self.root)
        save_frame.pack(padx=10, pady=5, fill="x")
        
        save_result_btn = ttk.Button(save_frame, text="保存处理结果", command=self.save_processed_excel)
        save_result_btn.pack(side="left", padx=5, pady=5)
        
        # 添加保存图表按钮
        save_chart_btn = ttk.Button(save_frame, text="保存图表", command=self.save_chart)
        save_chart_btn.pack(side="left", padx=5, pady=5)
        
        # 标记按钮已存在
        setattr(save_frame, 'save_result_btn', True)
    
    def save_processed_excel(self):
        """保存处理后的Excel文件"""
        if self.current_excel_df is None:
            self.update_chat("错误: 没有可保存的数据")
            return
            
        file_path = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            filetypes=[("Excel文件", "*.xlsx")],
            initialfile="处理结果.xlsx"
        )
        if not file_path:
            return
            
        try:
            self.current_excel_df.to_excel(file_path, index=False)
            self.update_chat(f"处理结果已保存到: {file_path}")
        except Exception as e:
            self.update_chat(f"保存Excel文件失败: {str(e)}")
    
    def save_chart(self):
        """保存当前图表"""
        if self.current_figure is None:
            self.update_chat("错误: 没有可保存的图表")
            return
            
        file_path = filedialog.asksaveasfilename(
            defaultextension=".png",
            filetypes=[("PNG图片", "*.png"), ("PDF文件", "*.pdf"), ("SVG图片", "*.svg")],
            initialfile="数据可视化.png"
        )
        if not file_path:
            return
            
        try:
            self.current_figure.savefig(file_path, dpi=300, bbox_inches='tight')
            self.update_chat(f"图表已保存到: {file_path}")
        except Exception as e:
            self.update_chat(f"保存图表失败: {str(e)}")
    
    def save_history(self):
        """保存对话历史"""
        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt")]
        )
        if not file_path:
            return
            
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                for message in self.conversation_history:
                    role = message['role'].capitalize()
                    content = message['content'].replace('\n', ' ')
                    f.write(f"{role}: {content}\n")
            self.update_chat(f"对话历史已保存到 {file_path}")
        except Exception as e:
            self.update_chat(f"保存文件失败: {str(e)}")
    
    def clear_history(self):
        """清空聊天记录和上下文"""
        # 清空对话历史
        self.conversation_history = []
        
        # 清空聊天显示
        self.chat_display.config(state="normal")
        self.chat_display.delete("1.0", tk.END)
        self.chat_display.config(state="disabled")
        
        # 重置PandasAI助手
        if self.current_excel_df is not None:
            self.pandasai_helper = PandasAIHelper(model_name=self.model_name.get())
            self.pandasai_helper.initialize(self.current_excel_df)
        
        self.update_chat("聊天记录已清空，上下文已重置")
    
    def translate_text(self):
        """翻译对话框中的文本"""
        # 获取用户输入
        text = self.user_input.get("1.0", tk.END).strip()
        
        if not text:
            self.update_chat("请输入需要翻译的内容")
            return
            
        # 判断文本语言
        is_chinese = any('\u4e00' <= char <= '\u9fff' for char in text)
        
        # 设置翻译提示
        if is_chinese:
            # 中文翻译成英文
            system_prompt = "你是一个好用的翻译助手。请将我的中文翻译成英文。我发给你所有的话都是需要翻译的内容，你只需要回答翻译结果。翻译结果请符合英文的语言习惯。"
            self.update_chat(f"正在将中文翻译为英文...")
        else:
            # 英文翻译成中文
            system_prompt = "你是一个好用的翻译助手。请将我的英文翻译成中文。我发给你所有的话都是需要翻译的内容，你只需要回答翻译结果。翻译结果请符合中文的语言习惯。"
            self.update_chat(f"正在将英文翻译为中文...")
        
        # 调用模型进行翻译
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": text}
        ]
        
        # 获取翻译结果
        translation = self.ollama_api.chat(self.model_name.get(), messages)
        
        # 显示翻译结果
        self.update_chat(f"翻译结果: {translation}")
        
        # 将翻译结果放入输入框
        self.user_input.delete("1.0", tk.END)
        self.user_input.insert(tk.END, translation)
    
    def update_chat(self, message):
        """更新聊天显示"""
        self.chat_display.config(state="normal")
        self.chat_display.insert(tk.END, message + "\n")
        self.chat_display.config(state="disabled")
        self.chat_display.see(tk.END)


# 主程序
if __name__ == "__main__":
    root = tk.Tk()
    root.geometry("900x700")
    app = ExcelAIApp(root)
    root.mainloop()
