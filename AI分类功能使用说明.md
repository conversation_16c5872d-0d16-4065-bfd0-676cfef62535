# AI智能分类标注功能使用说明

## 功能概述

AI智能分类标注功能使用本地Ollama大模型对Excel/CSV数据进行智能分类，支持预设配置和自定义分类标准，大幅提高分类准确度和效率。

## 主要优化特性

### 1. 预设分类配置
- **情感分析**：对文本内容进行正面/负面/中性分类
- **产品类别**：对产品名称进行电子产品/服装鞋帽/食品饮料等分类
- **客户类型**：对客户进行VIP/普通/新客户/流失客户分类
- **优先级分类**：对任务进行高/中/低优先级分类
- **地区分类**：对地址进行一线/新一线/二线/三线及以下城市分类

### 2. 智能提示词优化
- 自动生成结构化提示词
- 包含分类标准、示例和输出格式要求
- 根据数据样本优化分类准确度

### 3. 配置文件管理
- 可视化配置管理界面
- 支持创建、编辑、删除自定义配置
- JSON格式配置文件，易于维护和分享

### 4. 增强的错误处理
- 智能处理空值和异常数据
- 结果数量验证和错误统计
- 详细的进度显示和状态反馈

## 使用步骤

### 1. 基本使用流程

1. **加载数据**：点击"加载数据"按钮，选择Excel或CSV文件
2. **启动分类**：点击"AI分类标注"按钮
3. **选择数据列**：从下拉框中选择要分类的列
4. **选择配置**：
   - 选择预设配置：从"预设配置"下拉框选择合适的配置
   - 自定义配置：选择"自定义配置"并手动输入分类标准
5. **设置参数**：
   - 新列名：指定分类结果保存的列名
   - 批处理大小：建议1-5，平衡精确度和速度
6. **执行分类**：点击"确定"开始AI分类

### 2. 预设配置使用

选择预设配置后，系统会自动：
- 加载对应的分类标准
- 显示预设类别和示例
- 优化提示词以提高准确度

### 3. 自定义配置

如果预设配置不满足需求，可以：
- 选择"自定义配置"
- 在分类标准文本框中详细描述分类规则
- 越具体的描述，AI分类结果越准确

## 配置管理器使用

### 1. 打开配置管理器
在AI分类标注界面点击"管理配置"按钮

### 2. 创建新配置
1. 在右侧填写配置信息：
   - **配置名称**：唯一的配置名称
   - **描述**：配置的简要说明
   - **分类标准**：详细的分类规则和标准
   - **类别**：用逗号分隔的类别列表
   - **示例**：JSON格式的分类示例

2. 点击"保存配置"

### 3. 编辑现有配置
1. 在左侧列表选择要编辑的配置
2. 修改右侧的配置信息
3. 点击"保存配置"

### 4. 删除配置
1. 在左侧列表选择要删除的配置
2. 点击"删除配置"
3. 确认删除

## 配置文件格式

配置文件采用JSON格式，结构如下：

```json
{
  "分类配置": {
    "配置名称": {
      "描述": "配置描述",
      "分类标准": "详细的分类标准和规则",
      "类别": ["类别1", "类别2", "类别3"],
      "示例": {
        "类别1": ["示例1", "示例2"],
        "类别2": ["示例3", "示例4"]
      }
    }
  }
}
```

## 最佳实践

### 1. 分类标准编写
- **具体明确**：避免模糊的描述，提供清晰的判断标准
- **互斥完整**：确保类别之间不重叠，覆盖所有可能情况
- **提供示例**：为每个类别提供典型示例
- **考虑边界**：明确边界情况的处理方式

### 2. 参数设置
- **批处理大小**：
  - 1-2：最高精确度，适合重要数据
  - 3-5：平衡精确度和速度，推荐设置
  - 5+：更快速度，可能降低精确度

### 3. 数据准备
- **清理数据**：移除明显的错误和无关数据
- **统一格式**：确保同类数据格式一致
- **处理空值**：预先处理空值和缺失数据

### 4. 结果验证
- **抽样检查**：对分类结果进行抽样验证
- **统计分析**：查看分类结果的分布是否合理
- **人工复核**：对重要数据进行人工复核

## 注意事项

### 1. 模型要求
- 需要本地运行Ollama服务
- 推荐使用qwen2.5-coder:7b或类似模型
- 确保模型已正确加载

### 2. 性能考虑
- 大数据集建议分批处理
- 网络连接稳定性影响处理速度
- 模型大小影响分类精确度

### 3. 准确度说明
- AI分类结果可能不是100%准确
- 建议对重要数据进行人工复核
- 可根据实际效果调整配置

### 4. 数据安全
- 所有处理在本地完成，数据不会上传到云端
- 配置文件保存在本地，可以备份和分享
- 支持自动保存到原始文件

## 故障排除

### 1. 连接问题
- 确保Ollama服务正在运行
- 检查服务地址和端口配置
- 验证模型是否正确加载

### 2. 分类结果异常
- 检查分类标准是否清晰
- 调整批处理大小
- 尝试不同的预设配置

### 3. 性能问题
- 减小批处理大小
- 检查系统资源使用情况
- 考虑使用更小的模型

## 技术支持

如遇到问题，请检查：
1. Ollama服务状态
2. 模型加载情况
3. 配置文件格式
4. 数据格式和内容

更多技术细节请参考源代码注释和配置文件示例。
