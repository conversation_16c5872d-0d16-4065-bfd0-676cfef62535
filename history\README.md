# Excel自然语言处理工具

这个工具使用大语言模型(LLM)实现了通过自然语言对Excel文件进行操作和分析的功能。用户可以使用日常语言描述需求，系统会自动生成并执行相应的pandas代码来处理数据。

## 功能特点

- 使用自然语言查询和分析Excel数据
- 支持数据可视化（图表生成）
- 支持数据筛选、分组、聚合等常见操作
- 可保存处理结果和生成的图表
- 与本地Ollama模型集成，支持多种大语言模型
- 内置代码编辑器，允许用户修改生成的代码

## 安装步骤

1. 确保您已安装以下Python包：

```bash
pip install pandas matplotlib numpy requests
```

2. 确保您已经安装并运行了Ollama服务，默认API端点为`http://localhost:10000/api`

## 使用方法

1. 运行程序：

```bash
python local_nlp_excel.py
```

2. 在界面中点击"加载Excel"按钮，选择要分析的Excel文件

3. 在输入框中输入自然语言查询，例如：
   - "计算销售额的平均值和总和"
   - "按地区对数据进行分组并计算每组的统计数据"
   - "创建一个销售额随时间变化的折线图"
   - "筛选出销售额大于1000的记录"

4. 查看结果，可以选择保存处理后的数据或生成的图表

## 示例查询

以下是一些您可以尝试的自然语言查询示例（假设您有一个包含销售数据的Excel文件）：

- "显示前10行数据"
- "计算每个产品的销售总额"
- "找出销售额最高的前5个客户"
- "按月份统计销售额并绘制折线图"
- "计算每个地区的销售额占比并绘制饼图"
- "找出哪些产品的销售额超过了平均值"
- "按季度计算销售额并与去年同期比较"

## 工作原理

1. 用户输入自然语言查询，如"计算每个地区的销售总额并绘制饼图"
2. 系统将查询发送给大语言模型，生成相应的pandas代码
3. 系统自动执行生成的代码，并显示结果（数据或图表）
4. 如果代码执行失败，系统会显示代码编辑器，让用户可以修改代码并重新执行

## 与原版的区别

相比原来的Excel处理工具，这个新版本有以下改进：

1. 使用自然语言生成pandas代码，无需用户手动编写代码
2. 更智能的数据理解和处理能力
3. 自动生成适合的可视化图表
4. 内置代码编辑器，允许用户在需要时修改生成的代码
5. 更简洁的用户界面和交互流程

## 注意事项

- 查询的质量取决于所使用的语言模型，更强大的模型通常能提供更准确的结果
- 对于复杂的查询，可能需要更详细的描述
- 如果生成的代码执行失败，您可以在代码编辑器中修改并重新执行
