# PandasAI 本地大模型集成工具

本项目提供通过本地大模型(Ollama)使用PandasAI进行数据分析的实现，使用官方的pandasai-litellm集成包。

## 特性

- 支持使用自然语言查询数据
- 自动生成数据可视化图表
- 提供交互式GUI界面操作Excel数据
- 使用pandasai-litellm提供高效的大模型集成方式
- temperature设置为0，确保输出结果的一致性

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 图形界面应用

运行图形界面应用程序：

```bash
python local_pandasaiapi.py
```

图形界面使用说明：
1. 启动后，选择要使用的模型
2. 点击"加载Excel"按钮加载数据
3. 输入自然语言查询并发送
4. 查看结果，可以导出处理后的数据或图表

### 命令行演示

运行演示脚本，展示LiteLLM方式的数据分析：

```bash
python demo_pandasai.py
```

## 文件说明

- `local_pandasaiapi.py` - 主应用程序，提供完整的GUI界面
- `local_pandasai_litellm.py` - 使用pandasai-litellm包的实现
- `demo_pandasai.py` - 简单的演示脚本
- `requirements.txt` - 依赖包列表

## 需求

- Python 3.9+
- Ollama本地部署（确保端口11434可访问）
- 推荐模型：llama3, qwen2.5-coder等大型语言模型

## 注意事项

1. 确保Ollama服务已启动并可访问
2. 必须安装pandasai-litellm包，否则程序无法运行
3. 图形界面需要tkinter支持
4. 数据可视化需要matplotlib库
5. 所有大模型查询的temperature设置为0，确保结果一致性

## 许可

MIT 