{"分类配置": {"问题原因分类": {"描述": "基于质量管理体系的问题原因分类标准，适用于产品质量问题的根因分析和分类", "分类标准": "根据问题发生的阶段和性质，将质量问题原因分为设计阶段、软件阶段、工艺阶段、管理阶段、元器件阶段、外协外购件阶段、使用阶段等七大类别，每个大类下包含具体的子分类。分类时需要准确识别问题的根本原因所在的阶段和具体类型。", "类别": ["外部需求", "需求分析", "接口设计", "功能性能和物理特性设计", "通用质量特性设计", "试验验证设计", "技术认知", "软件外部需求", "软件设计", "软件测试", "软件管理", "工艺设计", "工艺文件可操作性", "工艺不稳定", "工艺工装设计", "工艺认知", "制度", "培训", "责任制不落实", "人员违反规章制度", "人员疏忽大意", "人员违规操作", "元器件准入", "元器件设计", "元器件工艺", "元器件生产管理和操作", "元器件偶发失效", "元器件固有缺陷且未剔除", "外协外购件准入", "外协外购件设计", "外协外购件工艺", "外协外购件生产管理", "外协外购件偶发失效", "外协外购件固有缺陷且未剔除", "用户使用不当", "外部环境", "复试无故障", "其他"], "分类层次": {"设计阶段": ["外部需求", "需求分析", "接口设计", "功能性能和物理特性设计", "通用质量特性设计", "试验验证设计", "技术认知"], "软件阶段": ["软件外部需求", "软件设计", "软件测试", "软件管理"], "工艺阶段": ["工艺设计", "工艺文件可操作性", "工艺不稳定", "工艺工装设计", "工艺认知"], "管理阶段": ["制度", "培训", "责任制不落实", "人员违反规章制度", "人员疏忽大意", "人员违规操作"], "元器件阶段": ["元器件准入", "元器件设计", "元器件工艺", "元器件生产管理和操作", "元器件偶发失效", "元器件固有缺陷且未剔除"], "外协外购件阶段": ["外协外购件准入", "外协外购件设计", "外协外购件工艺", "外协外购件生产管理", "外协外购件偶发失效", "外协外购件固有缺陷且未剔除"], "使用阶段": ["用户使用不当", "外部环境", "复试无故障", "其他"]}, "详细定义": {"外部需求": "外部输入技术要求内容不全或有差错", "需求分析": "需求分析内容缺失或错误", "接口设计": "飞机与数控系统、发动机与数控系统、数控系统各产品之间、软件与硬件之间接口关系不协调、不匹配而造成的质量问题", "功能性能和物理特性设计": "架构、功能性能、物理特性设计过程中发生的错误，包括冗余、降额、电源特性设计、元器件选型错误，以及外包件设计要求错误等", "通用质量特性设计": "安全性、维护性、可靠性、保障性、测试性、环境适应性（含电磁兼容性、雷电防护、单粒子防护）设计不满足指标要求", "试验验证设计": "a）试验验证设计不合理、试验验证方案考虑不周、产品测试没有覆盖所有工况导致问题泄露（仅作为辅因）或产品损坏\nb）试验验证工装、设备设计或选型错误", "技术认知": "a）缺少预研基础，现有技术无法实现\nb）对新技术或新产品认知和研究不够", "软件外部需求": "外部要求描述不清、需求不完备等", "软件设计": "a）软件/PLD设计差错或编码差错\nb）软件/PLD与系统软件之间、软件各模块之间的接口设计不当\nc）软件/PLD容错、防错设计能力不足", "软件测试": "由于测试覆盖性不足，未能及时发现软件差错而导致的质里问题", "软件管理": "由于版本控制，或技术状态控制不当引起的质量问题", "工艺设计": "工艺方案内容缺失或错误、工艺方法差错、工艺流程差错、工艺参数（含生产程式和软件参数）不合理", "工艺文件可操作性": "工艺文件不细不全、可操作性差导致的问题", "工艺不稳定": "人、机、料、法、环发生变化时对工艺过程产生影响造成质量问题", "工艺工装设计": "焊接、组装、机加、测试用的工装设计问题", "工艺认知": "新工艺的认知和研究不够", "制度": "制度缺项，工作人员没有可以遵从的制度或规范，或规章制度规范不完善造成的质量问题", "培训": "有制度规范，但培训的内容、深度与力度不够，培训流于形式，造成工作人员不了解规章制度或岗位能力不足的质量问题", "责任制不落实": "责任制不落实，人员疏忽大意和把关不严等方面的质量问题", "人员违反规章制度": "相关人员违反规章制度，需要追究人为责任的质量问题", "人员疏忽大意": "由于操作人员疏忽大意造成的质量问题", "人员违规操作": "操作人员违反操作规章造成的质量问题", "元器件准入": "元器件准入验证不到位导致不满足产品使用需求，包括可焊性、常规功能性能符合性、环境适应性、可靠性等", "元器件设计": "元器件设计导致的问题，设计问题指设计原因导致元器件指标不满足详规及相关标准要求", "元器件工艺": "元器件工艺导致的问题", "元器件生产管理和操作": "元器件生产管理和操作导致的问题", "元器件偶发失效": "元器件偶发失效（无纠正措施的）导致的问题", "元器件固有缺陷且未剔除": "元器件固有缺陷且未剔除（只能加强检测的）导致的问题", "外协外购件准入": "外协外购件准入验证不到位导致不满足产品使用需求", "外协外购件设计": "机加件、印制板、电路模块、传感器、线缆组件、标准件等外购外协外包设计", "外协外购件工艺": "机加件、印制板、电路模块、传感器、线缆组件、标准件等外购外协外包工艺", "外协外购件生产管理": "机加件、印制板、电路模块、传感器、线缆组件、标准件等外购外协外包管理和操作", "外协外购件偶发失效": "机加件、印制板、电路模块、传感器、线缆组件、标准件等外购外协外包偶发失效", "外协外购件固有缺陷且未剔除": "机加件、印制板、电路模块、传感器、线缆组件、标准件等外购外协外包固有缺陷且未别除", "用户使用不当": "用户使用不当，用户未按使用维护说明书等要求操作导致的问题", "外部环境": "外部环境引发的问题，产品对外端口引入异常过电应力导致虚警或产品损坏，或设备/其他产品导致虚警或产品损坏", "复试无故障": "产品无故障", "其他": "以上分类无法覆盖的质量问题纳入此分类"}, "示例": {"外部需求": ["技术要求文档缺少关键参数", "外部接口规范描述不清"], "需求分析": ["功能需求理解错误", "性能指标分析遗漏"], "接口设计": ["硬件接口不匹配", "软件接口协议错误", "系统间通信异常"], "功能性能和物理特性设计": ["电路设计错误", "结构设计不合理", "元器件选型错误"], "软件设计": ["代码逻辑错误", "算法实现问题", "模块接口设计缺陷"], "工艺设计": ["焊接工艺参数不当", "装配流程错误", "测试工艺缺陷"], "制度": ["缺少操作规范", "质量标准不完善"], "人员疏忽大意": ["操作失误", "检查遗漏", "记录错误"], "元器件准入": ["器件性能验证不足", "环境适应性测试缺失"], "外协外购件设计": ["外购模块设计缺陷", "标准件规格错误"], "用户使用不当": ["违规操作", "维护不当", "超规格使用"]}}}, "使用说明": {"分类原则": ["1. 按照问题发生的根本原因进行分类，而非表面现象", "2. 优先考虑设计阶段问题，其次是工艺、管理等阶段", "3. 对于复合原因，选择主要原因进行分类", "4. 无法明确分类的问题归入'其他'类别"], "分类流程": ["1. 分析问题描述，识别关键信息", "2. 判断问题发生的主要阶段（设计/软件/工艺/管理/元器件/外协外购件/使用）", "3. 在对应阶段中选择最匹配的具体分类", "4. 参考详细定义确认分类准确性"]}}