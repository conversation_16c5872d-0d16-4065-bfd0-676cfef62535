import requests
import json
import os  # 用于文件路径处理
import pandas as pd  # 用于Excel数据处理
import matplotlib.pyplot as plt  # 用于数据可视化
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg  # 用于在tkinter中显示matplotlib图表
import numpy as np  # 用于数学计算
from pandasai import Agent  # 导入PandasAI Agent
from pandasai.llm import OpenAI  # 导入OpenAI LLM，用于连接Ollama

# 配置API端点
BASE_URL = "http://localhost:11434/api"  # 基础 URL，调整为更通用形式

def get_available_models():
    """主动获取服务器上的可用模型列表"""
    tags_url = f"{BASE_URL}/tags"  # 假设使用 Ollama 的 /api/tags 端点
    try:
        response = requests.get(tags_url)
        if response.status_code == 200:
            result = response.json()
            # 提取模型名称，假设响应结构如 {"models": [{"name": "model1"}, ...]}
            if 'models' in result and isinstance(result['models'], list):
                models = [model.get('name') for model in result['models'] if 'name' in model]
                return models  # 返回列表，如 ["grok", "llama3"]
            else:
                return []  # 如果结构不匹配，返回空列表
        else:
            return []  # 请求失败
    except requests.exceptions.RequestException as e:
        print(f"获取模型列表失败: {str(e)}")
        return []  # 网络错误，返回空列表

def read_file(file_path):
    """读取文件内容"""
    if not os.path.exists(file_path):
        return "错误: 文件不存在。"
    
    extension = os.path.splitext(file_path)[1].lower()
    
    if extension in ['.xlsx', '.xls']:
        try:
            # 使用pandas读取Excel文件
            df = pd.read_excel(file_path)
            text = df.head(10).to_string()  # 只显示前10行
            return f"提取的Excel内容:\n{text}...（内容可能被截断，如果过长）"
        except Exception as e:
            return f"读取Excel失败: {str(e)}"
    
    elif extension == '.txt':
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                text = f.read()
            return f"文本文件内容:\n{text[:500]}...（内容可能被截断，如果过长）"
        except Exception as e:
            return f"读取文本文件失败: {str(e)}"
    else:
        return "错误: 不支持的文件格式。只支持 .xlsx, .xls, .txt。"

def initialize_pandasai(df=None, model_name="grok"):
    """初始化PandasAI对象"""
    try:
        # 创建OpenAI LLM实例，但配置为使用Ollama API
        llm = OpenAI(
            api_token="not-needed",  # Ollama不需要API token
            model=model_name,
            api_base=BASE_URL,  # 使用BASE_URL作为api_base
        
        # 创建PandasAI Agent实例
        pandas_ai = Agent(
            [df] if df is not None else [],  # 传入数据框列表，如果有的话
            config={"llm": llm}  # 传入LLM实例
        )
        
        return pandas_ai
    except Exception as e:
        print(f"初始化PandasAI失败: {str(e)}")
        return None

def process_excel_with_pandasai(file_path):
    """使用pandasai处理Excel文件，准备自然语言交互"""
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            return {
                "success": False,
                "dataframe": None,
                "info": None,
                "message": f"错误: 文件不存在: '{file_path}'"
            }
            
        # 检查文件扩展名是否为Excel
        extension = os.path.splitext(file_path)[1].lower()
        if extension not in ['.xlsx', '.xls']:
            return {
                "success": False,
                "dataframe": None,
                "info": None,
                "message": f"错误: 不支持的文件格式: '{extension}'，只支持.xlsx和.xls"
            }
            
        # 读取Excel文件
        df = pd.read_excel(file_path)
        
        # 返回数据框的基本信息
        info = {
            "columns": list(df.columns),
            "shape": df.shape,
            "dtypes": {col: str(dtype) for col, dtype in df.dtypes.items()},
            "head": df.head(5).to_dict('records'),
            "describe": df.describe().to_dict() if len(df.select_dtypes(include=['number']).columns) > 0 else None
        }
        
        return {
            "success": True,
            "dataframe": df,
            "info": info,
            "message": "Excel文件已成功加载，可以使用自然语言进行查询"
        }
    except Exception as e:
        return {
            "success": False,
            "dataframe": None,
            "info": None,
            "message": f"处理Excel文件时出错: {str(e)}"
        }

def chat_with_model(messages, model_name="grok"):
    """与模型对话"""
    headers = {
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": model_name,
        "messages": messages,  # 传递整个对话历史
        "stream": False
    }
    
    chat_url = f"{BASE_URL}/chat"  # 完整的聊天端点
    try:
        response = requests.post(chat_url, headers=headers, data=json.dumps(payload))
        
        if response.status_code == 200:
            result = response.json()
            # 尝试从不同可能的响应结构中提取内容
            if 'message' in result and 'content' in result['message']:
                return result['message']['content']
            elif 'choices' in result and len(result['choices']) > 0 and 'message' in result['choices'][0]:
                return result['choices'][0]['message']['content']
            elif 'response' in result:
                return result['response']
            else:
                return f"响应格式未知: {json.dumps(result)}"
        else:
            return f"请求失败，状态码: {response.status_code}, 错误信息: {response.text}"
    
    except requests.exceptions.RequestException as e:
        return f"网络错误: {str(e)}"
    except Exception as e:
        return f"其他错误: {str(e)}"

import tkinter as tk
from tkinter import ttk, filedialog, scrolledtext

class ChatGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("AI模型对话工具 - PandasAI版")
        
        # 初始化变量
        self.model_name = tk.StringVar(value="grok")
        self.conversation_history = []
        self.current_excel_df = None  # 存储当前处理的Excel数据框
        self.current_excel_path = None  # 存储当前处理的Excel文件路径
        self.current_figure = None  # 存储当前的可视化图表
        self.canvas = None  # 存储matplotlib画布
        self.pandas_ai = None  # 存储PandasAI实例
        
        # 创建界面
        self.create_widgets()
        self.load_models()
    
    def create_widgets(self):
        # 模型选择区域
        model_frame = ttk.LabelFrame(self.root, text="模型选择")
        model_frame.pack(padx=10, pady=5, fill="x")
        
        self.model_combobox = ttk.Combobox(model_frame, textvariable=self.model_name, state="readonly")
        self.model_combobox.pack(padx=5, pady=5, fill="x")
        
        # 对话区域
        chat_frame = ttk.LabelFrame(self.root, text="对话")
        chat_frame.pack(padx=10, pady=5, expand=True, fill="both")
        
        self.chat_display = scrolledtext.ScrolledText(chat_frame, wrap=tk.WORD, state="disabled")
        self.chat_display.pack(padx=5, pady=5, expand=True, fill="both")
        
        # 输入区域
        input_frame = ttk.Frame(self.root)
        input_frame.pack(padx=10, pady=5, fill="x")
        
        # 使用Text控件替代Entry，增大输入框高度
        self.user_input = tk.Text(input_frame, height=4, wrap=tk.WORD)
        self.user_input.pack(padx=5, pady=5, fill="x")
        self.user_input.bind("<Return>", self.send_message)
        
        # 按钮区域
        button_frame = ttk.Frame(input_frame)
        button_frame.pack(fill="x")
        
        send_btn = ttk.Button(button_frame, text="发送", command=self.send_message)
        send_btn.pack(side="left", padx=5, pady=5)
        
        excel_btn = ttk.Button(button_frame, text="加载Excel", command=self.load_excel)
        excel_btn.pack(side="left", padx=5, pady=5)
        
        clear_btn = ttk.Button(button_frame, text="清空记录", command=self.clear_history)
        clear_btn.pack(side="right", padx=5, pady=5)
        
        save_btn = ttk.Button(button_frame, text="保存历史", command=self.save_history)
        save_btn.pack(side="right", padx=5, pady=5)
        
        # 可视化区域（初始隐藏）
        self.viz_frame = ttk.LabelFrame(self.root, text="数据可视化")
        # 不立即显示，等待有数据时再显示: self.viz_frame.pack(padx=10, pady=5, fill="both", expand=True)
    
    def load_models(self):
        """加载可用模型列表"""
        available_models = get_available_models()
        if available_models:
            self.model_combobox["values"] = available_models
            self.model_name.set(available_models[0])
        else:
            self.model_name.set("grok")
            self.update_chat("无法获取模型列表，默认使用 'grok'")
    
    def send_message(self, event=None):
        """发送用户消息"""
        user_input = self.user_input.get("1.0", tk.END).strip()
        if not user_input:
            return
            
        self.user_input.delete("1.0", tk.END)
        self.update_chat(f"您: {user_input}")
        
        # 检查是否是Excel查询
        if self.current_excel_df is not None and self.pandas_ai is not None:
            # 使用PandasAI处理Excel查询
            self.handle_excel_query(user_input)
        else:
            # 正常对话流程
            self.conversation_history.append({"role": "user", "content": user_input})
            response_text = chat_with_model(self.conversation_history, self.model_name.get())
            self.update_chat(f"模型: {response_text}")
            self.conversation_history.append({"role": "assistant", "content": response_text})
    
    def load_excel(self):
        """加载Excel文件并初始化PandasAI"""
        # 选择Excel文件
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls")]
        )
        if not file_path:
            return
            
        self.update_chat(f"已选择Excel文件: {file_path}")
        self.current_excel_path = file_path
        
        # 使用pandasai加载Excel文件
        result = process_excel_with_pandasai(file_path)
        
        if result["success"]:
            self.current_excel_df = result["dataframe"]
            
            # 初始化PandasAI
            self.pandas_ai = initialize_pandasai(self.current_excel_df, self.model_name.get())
            if self.pandas_ai is None:
                self.update_chat("初始化PandasAI失败，请检查模型配置")
                return
                
            # 显示基本信息
            info = result["info"]
            columns_str = ", ".join(str(col) for col in info["columns"])
            self.update_chat(f"Excel文件已加载，共{info['shape'][0]}行，{info['shape'][1]}列")
            self.update_chat(f"列名: {columns_str}")
            
            # 显示前5行数据预览
            preview = self.current_excel_df.head(5).to_string()
            self.update_chat(f"数据预览:\n{preview}")
            
            # 提示用户输入查询
            self.update_chat("\n现在您可以使用自然语言查询Excel数据，例如:")
            self.update_chat("- 计算销售额的平均值和总和")
            self.update_chat("- 按地区对数据进行分组并计算每组的统计数据")
            self.update_chat("- 创建一个销售额随时间变化的折线图")
            self.update_chat("- 筛选出销售额大于1000的记录")
            
        else:
            self.update_chat(f"加载Excel文件失败: {result['message']}")
    
    def handle_excel_query(self, query):
        """使用PandasAI处理Excel查询"""
        if self.current_excel_df is None or self.pandas_ai is None:
            self.update_chat("错误: 没有加载Excel文件或PandasAI未初始化")
            return
            
        self.update_chat("正在处理您的查询...")
        
        try:
            # 使用PandasAI处理查询
            result = self.pandas_ai.chat(query)
            
            # 检查结果类型
            if isinstance(result, pd.DataFrame):
                # 如果结果是DataFrame，显示前10行
                self.update_chat("查询结果:")
                preview = result.head(10).to_string()
                self.update_chat(preview)
                
                # 更新当前DataFrame为查询结果
                self.current_excel_df = result
                self.add_save_result_button()
                
            elif isinstance(result, str):
                # 如果结果是字符串，直接显示
                self.update_chat(f"查询结果: {result}")
                
            elif hasattr(result, '_repr_html_'):
                # 如果结果有HTML表示（如图表），尝试显示
                self.update_chat("生成了可视化结果")
                self.display_pandasai_result(result)
                
            else:
                # 其他类型的结果
                self.update_chat(f"查询结果: {str(result)}")
                
        except Exception as e:
            self.update_chat(f"处理查询时出错: {str(e)}")
            
            # 尝试使用常规对话模型作为备选
            self.update_chat("尝试使用常规对话模型处理...")
            
            # 构建提示信息
            df_info = {
                "columns": list(self.current_excel_df.columns),
                "shape": self.current_excel_df.shape,
                "head": self.current_excel_df.head(5).to_dict('records')
            }
            
            prompt = f"""
我有一个Excel数据集，具有以下特征:
- 行数: {df_info['shape'][0]}
- 列数: {df_info['shape'][1]}
- 列名: {', '.join(df_info['columns'])}
- 前5行数据: {json.dumps(df_info['head'], ensure_ascii=False, indent=2)}

用户的查询是: "{query}"

请提供详细的pandas代码来回答这个查询，并解释代码的作用。
"""
            
            # 添加到对话历史
            self.conversation_history.append({"role": "user", "content": prompt})
            
            # 获取模型响应
            response_text = chat_with_model(self.conversation_history, self.model_name.get())
            self.update_chat(f"模型建议: {response_text}")
            self.conversation_history.append({"role": "assistant", "content": response_text})
    
    def display_pandasai_result(self, result):
        """显示PandasAI的结果（特别是图表）"""
        try:
            # 检查是否是matplotlib图表
            if hasattr(result, 'figure') or isinstance(result, plt.figure.Figure):
                fig = result.figure if hasattr(result, 'figure') else result
                self.display_visualization(fig)
            else:
                # 尝试将结果转换为字符串并显示
                self.update_chat(str(result))
        except Exception as e:
            self.update_chat(f"显示结果时出错: {str(e)}")
    
    def display_visualization(self, fig):
        """在GUI中显示可视化图表"""
        # 清除之前的可视化
        if self.canvas:
            self.canvas.get_tk_widget().destroy()
        
        # 显示可视化区域
        if not self.viz_frame.winfo_ismapped():
            self.viz_frame.pack(padx=10, pady=5, fill="both", expand=True)
        
        # 创建新的画布
        self.canvas = FigureCanvasTkAgg(fig, master=self.viz_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill="both", expand=True)
        
        # 保存当前图表
        self.current_figure = fig
    
    def add_save_result_button(self):
        """添加保存结果按钮（如果不存在）"""
        # 检查按钮是否已存在
        for widget in self.root.winfo_children():
            if isinstance(widget, ttk.Frame) and hasattr(widget, 'save_result_btn'):
                return  # 按钮已存在
        
        # 创建保存结果按钮框架
        save_frame = ttk.Frame(self.root)
        save_frame.pack(padx=10, pady=5, fill="x")
        
        save_result_btn = ttk.Button(save_frame, text="保存处理结果", command=self.save_processed_excel)
        save_result_btn.pack(side="left", padx=5, pady=5)
        
        # 添加保存图表按钮
        save_chart_btn = ttk.Button(save_frame, text="保存图表", command=self.save_chart)
        save_chart_btn.pack(side="left", padx=5, pady=5)
        
        # 标记按钮已存在
        setattr(save_frame, 'save_result_btn', True)
    
    def save_processed_excel(self):
        """保存处理后的Excel文件"""
        if self.current_excel_df is None:
            self.update_chat("错误: 没有可保存的数据")
            return
            
        file_path = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            filetypes=[("Excel文件", "*.xlsx")],
            initialfile="处理结果.xlsx"
        )
        if not file_path:
            return
            
        try:
            self.current_excel_df.to_excel(file_path, index=False)
            self.update_chat(f"处理结果已保存到: {file_path}")
        except Exception as e:
            self.update_chat(f"保存Excel文件失败: {str(e)}")
    
    def save_chart(self):
        """保存当前图表"""
        if self.current_figure is None:
            self.update_chat("错误: 没有可保存的图表")
            return
            
        file_path = filedialog.asksaveasfilename(
            defaultextension=".png",
            filetypes=[("PNG图片", "*.png"), ("PDF文件", "*.pdf"), ("SVG图片", "*.svg")],
            initialfile="数据可视化.png"
        )
        if not file_path:
            return
            
        try:
            self.current_figure.savefig(file_path, dpi=300, bbox_inches='tight')
            self.update_chat(f"图表已保存到: {file_path}")
        except Exception as e:
            self.update_chat(f"保存图表失败: {str(e)}")
    
    def save_history(self):
        """保存对话历史"""
        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt")]
        )
        if not file_path:
            return
            
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                for message in self.conversation_history:
                    role = message['role'].capitalize()
                    content = message['content'].replace('\n', ' ')
                    f.write(f"{role}: {content}\n")
            self.update_chat(f"对话历史已保存到 {file_path}")
        except Exception as e:
            self.update_chat(f"保存文件失败: {str(e)}")
    
    def clear_history(self):
        """清空聊天记录"""
        self.conversation_history = []
        self.chat_display.config(state="normal")
        self.chat_display.delete("1.0", tk.END)
        self.chat_display.config(state="disabled")
        self.update_chat("聊天记录已清空")
    
    def update_chat(self, message):
        """更新聊天显示"""
        self.chat_display.config(state="normal")
        self.chat_display.insert(tk.END, message + "\n")
        self.chat_display.config(state="disabled")
        self.chat_display.see(tk.END)

# 主程序
if __name__ == "__main__":
    root = tk.Tk()
    root.geometry("800x600")
    app = ChatGUI(root)
    root.mainloop()
