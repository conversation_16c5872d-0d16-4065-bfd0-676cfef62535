import os
import pandasai as pai
from pandasai_litellm import LiteLLM
import pandas as pd


# 初始化本地Ollama模型
llm = LiteLLM(
    model="ollama/qwen2.5-coder:7b",  # 使用ollama/模型名称格式
    api_base="http://localhost:11434",  # Ollama默认本地API地址
    temperature=0,  # 控制生成结果的随机性
    max_tokens=2000  # 限制最大token数
)

pai.config.set({"llm": llm})  # 设置全局配置，使用本地Ollama模型

# 创建示例数据
data = pai.DataFrame({
    "Country": ["United States", "United Kingdom", "France", "Germany", "Italy"],
    "GDP": [214332, 294514, 283368, 387443, 216456],
    "Population": [334914, 68497914, 68042591, 83240566, 60317116]
})


# 使用自然语言查询数据
response = data.chat("哪个国家的人均GDP最高?计算并列出结果")

print(response)
