#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问题原因分类功能测试脚本
测试AI智能标注功能对质量问题原因的分类准确性
"""

import pandas as pd
import json
import os

def create_test_data():
    """创建测试数据"""
    test_problems = [
        "软件模块接口设计不当导致数据传输错误",
        "元器件选型错误，电容耐压不足导致击穿",
        "焊接工艺参数设置不当，导致虚焊",
        "操作人员未按规程操作，导致产品损坏",
        "外购印制板设计缺陷，走线宽度不足",
        "需求分析阶段遗漏关键功能要求",
        "测试覆盖性不足，未发现软件缺陷",
        "工艺文件描述不清，操作人员理解错误",
        "元器件供应商质量管控不严，器件失效",
        "用户违规使用，超出设备额定参数",
        "外部电磁干扰导致设备误动作",
        "设计阶段电路冗余设计不足",
        "工装设计错误，装配精度不够",
        "培训不到位，操作人员技能不足",
        "版本控制混乱，软件状态不一致",
        "试验验证方案考虑不周，未覆盖极限工况",
        "外协件准入验证不充分",
        "制度缺失，无相关操作规范",
        "责任制不落实，质量把关不严",
        "新技术认知不足，设计方案不成熟",
        "复试后未发现明显故障",
        "环境适应性设计不满足要求",
        "人员疏忽大意，检查遗漏",
        "外部技术要求描述不清晰",
        "其他未知原因导致的问题"
    ]
    
    # 对应的期望分类结果（用于验证）
    expected_results = [
        "软件设计",
        "功能性能和物理特性设计", 
        "工艺设计",
        "人员违规操作",
        "外协外购件设计",
        "需求分析",
        "软件测试",
        "工艺文件可操作性",
        "元器件生产管理和操作",
        "用户使用不当",
        "外部环境",
        "功能性能和物理特性设计",
        "工艺工装设计",
        "培训",
        "软件管理",
        "试验验证设计",
        "外协外购件准入",
        "制度",
        "责任制不落实",
        "技术认知",
        "复试无故障",
        "通用质量特性设计",
        "人员疏忽大意",
        "外部需求",
        "其他"
    ]
    
    # 确保所有数组长度一致
    num_problems = len(test_problems)
    severity_pattern = ['高', '中', '低'] * (num_problems // 3 + 1)

    df = pd.DataFrame({
        '问题描述': test_problems,
        '期望分类': expected_results,
        '问题编号': [f'P{i+1:03d}' for i in range(num_problems)],
        '严重程度': severity_pattern[:num_problems]
    })

    return df

def load_classification_config():
    """加载问题原因分类配置"""
    config_file = "ai_categorize_config.json"
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
            return config.get("分类配置", {}).get("问题原因分类", {})
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        return {}

def analyze_classification_accuracy(df_result):
    """分析分类准确性"""
    if 'AI分类结果' not in df_result.columns or '期望分类' not in df_result.columns:
        print("缺少必要的列进行准确性分析")
        return
    
    total = len(df_result)
    correct = sum(df_result['AI分类结果'] == df_result['期望分类'])
    accuracy = correct / total * 100
    
    print(f"\n=== 分类准确性分析 ===")
    print(f"总样本数: {total}")
    print(f"正确分类: {correct}")
    print(f"分类准确率: {accuracy:.1f}%")
    
    # 分析错误分类
    errors = df_result[df_result['AI分类结果'] != df_result['期望分类']]
    if len(errors) > 0:
        print(f"\n错误分类详情 ({len(errors)}个):")
        for idx, row in errors.iterrows():
            print(f"- 问题: {row['问题描述'][:50]}...")
            print(f"  期望: {row['期望分类']}")
            print(f"  实际: {row['AI分类结果']}")
            print()

def generate_classification_report(df_result, config):
    """生成分类报告"""
    if 'AI分类结果' not in df_result.columns:
        print("缺少AI分类结果列")
        return
    
    print("\n=== 分类结果统计 ===")
    classification_counts = df_result['AI分类结果'].value_counts()
    
    # 按分类层次统计
    categories = config.get('类别', [])
    stage_mapping = {
        '设计阶段': ['外部需求', '需求分析', '接口设计', '功能性能和物理特性设计', '通用质量特性设计', '试验验证设计', '技术认知'],
        '软件阶段': ['软件外部需求', '软件设计', '软件测试', '软件管理'],
        '工艺阶段': ['工艺设计', '工艺文件可操作性', '工艺不稳定', '工艺工装设计', '工艺认知'],
        '管理阶段': ['制度', '培训', '责任制不落实', '人员违反规章制度', '人员疏忽大意', '人员违规操作'],
        '元器件阶段': ['元器件准入', '元器件设计', '元器件工艺', '元器件生产管理和操作', '元器件偶发失效', '元器件固有缺陷且未剔除'],
        '外协外购件阶段': ['外协外购件准入', '外协外购件设计', '外协外购件工艺', '外协外购件生产管理', '外协外购件偶发失效', '外协外购件固有缺陷且未剔除'],
        '使用阶段': ['用户使用不当', '外部环境', '复试无故障', '其他']
    }
    
    print("\n按阶段统计:")
    for stage, stage_categories in stage_mapping.items():
        stage_count = sum(classification_counts.get(cat, 0) for cat in stage_categories)
        if stage_count > 0:
            print(f"{stage}: {stage_count}个问题")
            for cat in stage_categories:
                count = classification_counts.get(cat, 0)
                if count > 0:
                    print(f"  - {cat}: {count}")
    
    print(f"\n详细分类统计:")
    for category, count in classification_counts.items():
        percentage = count / len(df_result) * 100
        print(f"{category}: {count} ({percentage:.1f}%)")

def main():
    """主函数"""
    print("=== 问题原因分类功能测试 ===")
    
    # 创建测试数据
    print("1. 创建测试数据...")
    df_test = create_test_data()
    
    # 保存测试数据
    test_file = "问题原因分类测试数据.csv"
    df_test.to_csv(test_file, index=False, encoding='utf-8-sig')
    print(f"测试数据已保存到: {test_file}")
    
    # 加载分类配置
    print("\n2. 加载分类配置...")
    config = load_classification_config()
    if config:
        print(f"配置加载成功，包含 {len(config.get('类别', []))} 个分类类别")
        print("分类层次:")
        stage_mapping = {
            '设计阶段': 7, '软件阶段': 4, '工艺阶段': 5, '管理阶段': 6,
            '元器件阶段': 6, '外协外购件阶段': 6, '使用阶段': 4
        }
        for stage, count in stage_mapping.items():
            print(f"  {stage}: {count}个子类别")
    else:
        print("配置加载失败")
        return
    
    print(f"\n3. 测试数据概览:")
    print(f"总问题数: {len(df_test)}")
    print(f"问题类型分布:")
    expected_counts = df_test['期望分类'].value_counts()
    for category, count in expected_counts.head(10).items():
        print(f"  {category}: {count}")
    
    print(f"\n4. 使用说明:")
    print("请在主程序中执行以下步骤:")
    print(f"1) 加载文件: {test_file}")
    print("2) 选择列: 问题描述")
    print("3) 选择配置: 问题原因分类")
    print("4) 执行AI智能分类")
    print("5) 查看分类结果并与期望分类对比")
    
    print(f"\n测试完成后，可以使用以下代码分析结果:")
    print("""
# 假设分类结果保存在result.csv中
df_result = pd.read_csv('result.csv', encoding='utf-8-sig')
analyze_classification_accuracy(df_result)
generate_classification_report(df_result, config)
""")

if __name__ == "__main__":
    main()
