import requests
import pandas as pd
from pandasai import SmartDataframe
import tkinter as tk
from tkinter import scrolledtext, messagebox, filedialog
from typing import List  # 为Python 3.8添加类型注解支持

class ChatApp:
    def __init__(self, root):
        self.root = root
        self.root.title("SiliconFlow 大模型对话")
        
        # 聊天历史区域
        self.chat_history = scrolledtext.ScrolledText(root, width=60, height=20, state='disabled')
        self.chat_history.pack(padx=10, pady=10)
        
        # 按钮区域
        self.button_frame = tk.Frame(root)
        self.button_frame.pack(padx=10, pady=5, fill=tk.X)
        
        self.excel_button = tk.Button(self.button_frame, text="处理Excel", command=self.process_excel)
        self.excel_button.pack(side=tk.LEFT)
        
        # 输入区域
        self.input_frame = tk.Frame(root)
        self.input_frame.pack(padx=10, pady=5, fill=tk.X)
        
        self.input_text = tk.Text(self.input_frame, height=4, width=50)
        self.input_text.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        self.send_button = tk.Button(self.input_frame, text="发送", command=self.send_message)
        self.send_button.pack(side=tk.RIGHT, padx=5)
        
        # API配置
        self.url = "https://api.siliconflow.cn/v1/chat/completions"
        self.headers = {
            "Authorization": "Bearer sk-ilfrcfnozsggzjzwsmwuulggvclvedhskikediishrekugxn",
            "Content-Type": "application/json"
        }
        
    def send_message(self):
        user_input = self.input_text.get("1.0", tk.END).strip()
        if not user_input:
            return
        
        self.update_chat(f"You: {user_input}\n\n")
        self.input_text.delete("1.0", tk.END)
        
        if hasattr(self, 'excel_path'):
            self.process_excel_request(user_input)
        else:
            payload = {
                "model": "Qwen/Qwen2.5-Coder-7B-Instruct",
                "messages": [{"role": "user", "content": user_input}],
                "stream": False,
                "max_tokens": 512,
                "temperature": 0.7
            }
            
            try:
                response = requests.post(self.url, json=payload, headers=self.headers)
                response_data = response.json()
                
                if 'choices' in response_data and len(response_data['choices']) > 0:
                    assistant_reply = response_data['choices'][0]['message']['content']
                    self.update_chat(f"AI: {assistant_reply}\n\n")
            except Exception as e:
                messagebox.showerror("错误", f"API请求失败: {str(e)}")
    
    def update_chat(self, message):
        self.chat_history.config(state='normal')
        self.chat_history.insert(tk.END, message)
        self.chat_history.config(state='disabled')
        self.chat_history.see(tk.END)
        
    def process_excel(self):
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls"), ("所有文件", "*.*")]
        )
        if file_path:
            self.excel_path = file_path
            self.update_chat(f"已选择Excel文件: {file_path}\n请输入处理需求:\n")
        
    def process_excel_request(self, requirement):
        try:
            # 使用pandas AI处理Excel
            df = pd.read_excel(self.excel_path)
            smart_df = SmartDataframe(df)
            result = smart_df.chat(requirement)
            
            if isinstance(result, pd.DataFrame):
                result.to_excel(self.excel_path, index=False)
                self.update_chat(f"处理结果已保存到Excel文件\n")
            else:
                self.update_chat(f"处理结果: {str(result)}\n")
                
        except Exception as e:
            messagebox.showerror("错误", f"处理Excel失败: {str(e)}")

if __name__ == "__main__":
    root = tk.Tk()
    app = ChatApp(root)
    root.mainloop()
