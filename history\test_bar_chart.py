import tkinter as tk
from local_ai_fixed import ExcelSimpleApp

# 创建主窗口
root = tk.Tk()
root.geometry("900x700")

# 创建应用实例
app = ExcelSimpleApp(root)

# 手动测试：先加载数据
print("请先使用应用程序界面加载数据文件，然后执行以下操作：")
print("1. 点击'加载数据'按钮并选择一个数据文件")
print("2. 等待数据加载完成后，应用将自动创建'姓名'列的柱状图")

# 设置一个按钮来触发创建柱状图
def show_chart():
    if app.current_df is not None:
        app.create_bar_chart("姓名", "")
    else:
        print("请先加载数据")

chart_btn = tk.Button(root, text="创建姓名柱状图", command=show_chart)
chart_btn.pack(pady=10)

# 启动主循环
root.mainloop() 