import requests
import json
import os  # 用于文件路径处理
from openpyxl import load_workbook  # Excel .xlsx
import xlrd  # Excel .xls
import docx  # Word .docx
import PyPDF2  # PDF
import pandas as pd  # 用于Excel数据处理
import matplotlib.pyplot as plt  # 用于数据可视化
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg  # 用于在tkinter中显示matplotlib图表
import numpy as np  # 用于数学计算

# 配置API端点
BASE_URL = "http://localhost:10000/api"  # 基础 URL，调整为更通用形式4

def get_available_models():
    """主动获取服务器上的可用模型列表"""
    tags_url = f"{BASE_URL}/tags"  # 假设使用 Ollama 的 /api/tags 端点
    try:
        response = requests.get(tags_url)
        if response.status_code == 200:
            result = response.json()
            # 提取模型名称，假设响应结构如 {"models": [{"name": "model1"}, ...]}
            if 'models' in result and isinstance(result['models'], list):
                models = [model.get('name') for model in result['models'] if 'name' in model]
                return models  # 返回列表，如 ["grok", "llama3"]
            else:
                return []  # 如果结构不匹配，返回空列表
        else:
            return []  # 请求失败
    except requests.exceptions.RequestException as e:
        print(f"获取模型列表失败: {str(e)}")
        return []  # 网络错误，返回空列表

def read_file(file_path):
    if not os.path.exists(file_path):
        return "错误: 文件不存在。"
    
    extension = os.path.splitext(file_path)[1].lower()
    
    if extension in ['.xlsx', '.xls']:
        try:
            if extension == '.xlsx':
                wb = load_workbook(file_path, read_only=True)
                sheet = wb.active
                if sheet is None:
                    return "错误: 无法读取Excel工作表。"
                text = "\n".join([" ".join(str(cell.value) if cell.value is not None else "" for cell in row) for row in sheet.iter_rows()])
            else:  # .xls
                wb = xlrd.open_workbook(file_path)
                sheet = wb.sheet_by_index(0)
                text = "\n".join([" ".join(str(sheet.cell_value(r, c)) for c in range(sheet.ncols)) for r in range(sheet.nrows)])
            return f"提取的Excel内容:\n{text[:500]}...（内容可能被截断，如果过长）"  # 限制输出长度
        except Exception as e:
            return f"读取Excel失败: {str(e)}"
    
    elif extension == '.docx':
        try:
            doc = docx.Document(file_path)
            text = "\n".join([paragraph.text for paragraph in doc.paragraphs])
            return f"提取的Word内容:\n{text[:500]}...（内容可能被截断，如果过长）"
        except Exception as e:
            return f"读取Word失败: {str(e)}"
    
    elif extension == '.pdf':
        try:
            with open(file_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                text = ""
                for page in reader.pages:
                    text += page.extract_text() + "\n"
                return f"提取的PDF内容:\n{text[:500]}...（内容可能被截断，如果过长）"
        except Exception as e:
            return f"读取PDF失败: {str(e)}"
    
    elif extension == '.txt':
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                text = f.read()
            return f"文本文件内容:\n{text[:500]}...（内容可能被截断，如果过长）"
        except Exception as e:
            return f"读取文本文件失败: {str(e)}"
    else:
        return "错误: 不支持的文件格式。只支持 .xlsx, .xls, .docx, .pdf, .txt。"

def process_excel_with_pandas(file_path, instructions):
    """使用pandas处理Excel文件，根据自然语言指令"""
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            return {
                "success": False,
                "dataframe": None,
                "info": None,
                "message": f"错误: 文件不存在: '{file_path}'"
            }
            
        # 检查文件扩展名是否为Excel
        extension = os.path.splitext(file_path)[1].lower()
        if extension not in ['.xlsx', '.xls']:
            return {
                "success": False,
                "dataframe": None,
                "info": None,
                "message": f"错误: 不支持的文件格式: '{extension}'，只支持.xlsx和.xls"
            }
            
        # 读取Excel文件
        df = pd.read_excel(file_path)
        
        # 返回数据框的基本信息
        info = {
            "columns": list(df.columns),
            "shape": df.shape,
            "dtypes": {col: str(dtype) for col, dtype in df.dtypes.items()},
            "head": df.head(5).to_dict('records'),
            "describe": df.describe().to_dict() if len(df.select_dtypes(include=['number']).columns) > 0 else None
        }
        
        return {
            "success": True,
            "dataframe": df,
            "info": info,
            "message": "Excel文件已成功加载"
        }
    except Exception as e:
        return {
            "success": False,
            "dataframe": None,
            "info": None,
            "message": f"处理Excel文件时出错: {str(e)}"
        }

def create_visualization(df, viz_type, x_column=None, y_column=None, title="数据可视化"):
    """创建数据可视化图表"""
    try:
        fig, ax = plt.subplots(figsize=(10, 6))
        
        if viz_type == "bar":
            if x_column and y_column:
                df.plot(kind='bar', x=x_column, y=y_column, ax=ax)
            else:
                df.plot(kind='bar', ax=ax)
        elif viz_type == "line":
            if x_column and y_column:
                df.plot(kind='line', x=x_column, y=y_column, ax=ax)
            else:
                df.plot(kind='line', ax=ax)
        elif viz_type == "scatter":
            if x_column and y_column:
                df.plot(kind='scatter', x=x_column, y=y_column, ax=ax)
            else:
                return None, "散点图需要指定X和Y列"
        elif viz_type == "pie":
            if x_column:
                df[x_column].value_counts().plot(kind='pie', ax=ax)
            else:
                return None, "饼图需要指定分类列"
        elif viz_type == "hist":
            if x_column:
                df[x_column].plot(kind='hist', ax=ax)
            else:
                df.hist(ax=ax)
        else:
            return None, f"不支持的可视化类型: {viz_type}"
        
        plt.title(title)
        plt.tight_layout()
        
        return fig, "可视化创建成功"
    except Exception as e:
        return None, f"创建可视化时出错: {str(e)}"

def chat_with_model(messages, model_name="grok"):
    headers = {
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": model_name,
        "messages": messages,  # 传递整个对话历史
        "stream": False
    }
    
    chat_url = f"{BASE_URL}/chat"  # 完整的聊天端点
    try:
        response = requests.post(chat_url, headers=headers, data=json.dumps(payload))
        
        if response.status_code == 200:
            result = response.json()
            # 尝试从不同可能的响应结构中提取内容
            if 'message' in result and 'content' in result['message']:
                return result['message']['content']
            elif 'choices' in result and len(result['choices']) > 0 and 'message' in result['choices'][0]:
                return result['choices'][0]['message']['content']
            elif 'response' in result:
                return result['response']
            else:
                return f"响应格式未知: {json.dumps(result)}"
        else:
            return f"请求失败，状态码: {response.status_code}, 错误信息: {response.text}"
    
    except requests.exceptions.RequestException as e:
        return f"网络错误: {str(e)}"
    except Exception as e:
        return f"其他错误: {str(e)}"

import tkinter as tk
from tkinter import ttk, filedialog, scrolledtext

class ChatGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("AI模型对话工具")
        
        # 初始化变量
        self.model_name = tk.StringVar(value="grok")
        self.conversation_history = []
        self.current_excel_df = None  # 存储当前处理的Excel数据框
        self.current_excel_path = None  # 存储当前处理的Excel文件路径
        self.current_figure = None  # 存储当前的可视化图表
        self.canvas = None  # 存储matplotlib画布
        self.code_editor_window = None  # 代码编辑窗口
        self.current_code_blocks = []  # 当前的代码块
        
        # 创建界面
        self.create_widgets()
        self.load_models()
    
    def create_widgets(self):
        # 模型选择区域
        model_frame = ttk.LabelFrame(self.root, text="模型选择")
        model_frame.pack(padx=10, pady=5, fill="x")
        
        self.model_combobox = ttk.Combobox(model_frame, textvariable=self.model_name, state="readonly")
        self.model_combobox.pack(padx=5, pady=5, fill="x")
        
        # 对话区域
        chat_frame = ttk.LabelFrame(self.root, text="对话")
        chat_frame.pack(padx=10, pady=5, expand=True, fill="both")
        
        self.chat_display = scrolledtext.ScrolledText(chat_frame, wrap=tk.WORD, state="disabled")
        self.chat_display.pack(padx=5, pady=5, expand=True, fill="both")
        
        # 输入区域
        input_frame = ttk.Frame(self.root)
        input_frame.pack(padx=10, pady=5, fill="x")
        
        # 使用Text控件替代Entry，增大输入框高度
        self.user_input = tk.Text(input_frame, height=4, wrap=tk.WORD)
        self.user_input.pack(padx=5, pady=5, fill="x")
        self.user_input.bind("<Return>", self.send_message)
        
        # 按钮区域
        button_frame = ttk.Frame(input_frame)
        button_frame.pack(fill="x")
        
        send_btn = ttk.Button(button_frame, text="发送", command=self.send_message)
        send_btn.pack(side="left", padx=5, pady=5)
        
        file_btn = ttk.Button(button_frame, text="分析文件", command=self.analyze_file)
        file_btn.pack(side="left", padx=5, pady=5)
        
        excel_btn = ttk.Button(button_frame, text="Excel处理", command=self.process_excel)
        excel_btn.pack(side="left", padx=5, pady=5)
        
        clear_btn = ttk.Button(button_frame, text="清空记录", command=self.clear_history)
        clear_btn.pack(side="right", padx=5, pady=5)
        
        save_btn = ttk.Button(button_frame, text="保存历史", command=self.save_history)
        save_btn.pack(side="right", padx=5, pady=5)
        
        # 可视化区域（初始隐藏）
        self.viz_frame = ttk.LabelFrame(self.root, text="数据可视化")
        # 不立即显示，等待有数据时再显示: self.viz_frame.pack(padx=10, pady=5, fill="both", expand=True)
    
    def load_models(self):
        """加载可用模型列表"""
        available_models = get_available_models()
        if available_models:
            self.model_combobox["values"] = available_models
            self.model_name.set(available_models[0])
        else:
            self.model_name.set("grok")
            self.update_chat("无法获取模型列表，默认使用 'grok'")
    
    def send_message(self, event=None):
        """发送用户消息"""
        user_input = self.user_input.get("1.0", tk.END).strip()
        if not user_input:
            return
            
        self.user_input.delete("1.0", tk.END)
        self.update_chat(f"您: {user_input}")
        
        # 检查是否是Excel处理指令
        is_excel_instruction = False
        if len(self.conversation_history) > 0:
            last_message = self.conversation_history[-1]
            if last_message.get("role") == "system" and "Excel" in last_message.get("content", ""):
                is_excel_instruction = True
        
        # 添加用户消息到对话历史
        self.conversation_history.append({"role": "user", "content": user_input})
        
        if is_excel_instruction:
            # 处理Excel指令
            self.handle_excel_instruction(user_input)
        else:
            # 正常对话流程
            response_text = chat_with_model(self.conversation_history, self.model_name.get())
            self.update_chat(f"模型: {response_text}")
            self.conversation_history.append({"role": "assistant", "content": response_text})
    
    def analyze_file(self):
        """分析文件内容"""
        file_path = filedialog.askopenfilename(
            title="选择要分析的文件",
            filetypes=[("支持的文件", "*.xlsx *.xls *.docx *.pdf *.txt")]
        )
        if not file_path:
            return
            
        self.update_chat("正在分析文件，请稍候...")
        self.root.update()  # 强制更新界面显示
        
        file_content = read_file(file_path)
        if file_content.startswith("错误:"):
            self.update_chat(file_content)
            self.conversation_history.append({"role": "assistant", "content": file_content})
        else:
            enhanced_prompt = f"请分析以下文件内容: {file_content}"
            self.update_chat(f"分析文件: {file_path}")
            self.update_chat("文件加载完成，可进行提问")
            self.conversation_history.append({"role": "user", "content": enhanced_prompt})
    
    def save_history(self):
        """保存对话历史"""
        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt")]
        )
        if not file_path:
            return
            
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                for message in self.conversation_history:
                    role = message['role'].capitalize()
                    content = message['content'].replace('\n', ' ')
                    f.write(f"{role}: {content}\n")
            self.update_chat(f"对话历史已保存到 {file_path}")
        except Exception as e:
            self.update_chat(f"保存文件失败: {str(e)}")
    
    def clear_history(self):
        """清空聊天记录"""
        self.conversation_history = []
        self.chat_display.config(state="normal")
        self.chat_display.delete("1.0", tk.END)
        self.chat_display.config(state="disabled")
        self.update_chat("聊天记录已清空")

    def process_excel(self):
        """处理Excel文件"""
        # 选择Excel文件
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls")]
        )
        if not file_path:
            return
            
        self.update_chat(f"已选择Excel文件: {file_path}")
        self.current_excel_path = file_path
        
        # 使用pandas加载Excel文件
        result = process_excel_with_pandas(file_path, "")
        
        if result["success"]:
            self.current_excel_df = result["dataframe"]
            
            # 显示基本信息
            info = result["info"]
            columns_str = ", ".join(str(col) for col in info["columns"])
            self.update_chat(f"Excel文件已加载，共{info['shape'][0]}行，{info['shape'][1]}列")
            self.update_chat(f"列名: {columns_str}")
            
            # 显示前5行数据预览
            preview = self.current_excel_df.head(5).to_string()
            self.update_chat(f"数据预览:\n{preview}")
            
            # 提示用户输入处理指令
            self.update_chat("\n请输入您的Excel处理需求，例如:")
            self.update_chat("- 计算销售额的平均值和总和")
            self.update_chat("- 按地区对数据进行分组并计算每组的统计数据")
            self.update_chat("- 创建一个销售额随时间变化的折线图")
            self.update_chat("- 筛选出销售额大于1000的记录")
            
            # 添加特殊标记，表示下一条消息是Excel处理指令
            self.conversation_history.append({
                "role": "system", 
                "content": "用户已加载Excel文件，接下来的消息是Excel处理指令。"
            })
            
        else:
            self.update_chat(f"加载Excel文件失败: {result['message']}")
    
    def handle_excel_instruction(self, instruction):
        """处理用户的Excel处理指令"""
        if self.current_excel_df is None:
            self.update_chat("错误: 没有加载Excel文件，请先使用'Excel处理'按钮加载文件")
            return
            
        # 构建提示信息
        try:
            # 再次检查DataFrame是否有效
            if self.current_excel_df is None or not isinstance(self.current_excel_df, pd.DataFrame):
                self.update_chat("错误: Excel数据无效，请重新加载文件")
                return
                
            # 获取列名列表
            columns = list(self.current_excel_df.columns) if hasattr(self.current_excel_df, 'columns') else []
            
            df_info = {
                "columns": columns,
                "shape": self.current_excel_df.shape,
                "dtypes": {col: str(dtype) for col, dtype in self.current_excel_df.dtypes.items()},
                "head": self.current_excel_df.head(5).to_dict('records')
            }
        except Exception as e:
            self.update_chat(f"处理Excel数据时出错: {str(e)}")
            return
        
        prompt = f"""
我有一个Excel数据集，具有以下特征:
- 行数: {df_info['shape'][0]}
- 列数: {df_info['shape'][1]}
- 列名: {', '.join(df_info['columns'])}
- 数据类型: {json.dumps(df_info['dtypes'], ensure_ascii=False)}
- 前5行数据: {json.dumps(df_info['head'], ensure_ascii=False, indent=2)}

用户的处理需求是: "{instruction}"

请提供详细的pandas代码来实现这个需求，并解释代码的作用。如果需要可视化，请指定可视化类型(如bar, line, scatter, pie, hist)和相关参数。
代码应该是完整可执行的，假设数据框变量名为'df'。
"""
        
        # 添加到对话历史
        self.conversation_history.append({"role": "user", "content": prompt})
        
        # 获取模型响应
        self.update_chat("正在分析您的Excel处理需求...")
        response_text = chat_with_model(self.conversation_history, self.model_name.get())
        self.update_chat(f"模型: {response_text}")
        self.conversation_history.append({"role": "assistant", "content": response_text})
        
        # 尝试从响应中提取并执行pandas代码
        try:
            # 尝试提取代码块 - 改进的代码块提取逻辑
            code_blocks = []
            in_code_block = False
            code_buffer = []
            
            # 首先尝试提取标准的代码块（被```python和```包围的代码）
            for line in response_text.split('\n'):
                stripped_line = line.strip()
                if stripped_line.startswith('```') and ('python' in stripped_line.lower() or not in_code_block):
                    if in_code_block:  # 结束当前代码块
                        in_code_block = False
                        if code_buffer:  # 只有当缓冲区非空时才添加
                            code_blocks.append('\n'.join(code_buffer))
                    else:  # 开始新代码块
                        in_code_block = True
                        code_buffer = []
                elif in_code_block:
                    code_buffer.append(line)
            
            # 如果我们在循环结束时仍在代码块中，添加最后的代码块
            if in_code_block and code_buffer:
                code_blocks.append('\n'.join(code_buffer))
            
            # 如果没有明确的代码块，尝试查找可能的代码行
            if not code_blocks:
                self.update_chat("未找到明确的代码块，尝试提取可能的代码行...")
                potential_code = []
                for line in response_text.split('\n'):
                    # 更广泛地匹配可能的Python代码行
                    if any(keyword in line for keyword in ['df.', 'import', '=', 'print(', 'def ', 'for ', 'if ', '.plot', '.head', '.tail', '.describe', '.groupby']):
                        if not line.startswith('#') and not line.startswith('```'):
                            potential_code.append(line)
                if potential_code:
                    code_blocks.append('\n'.join(potential_code))
            
            # 如果找到代码，显示代码编辑窗口
            if code_blocks:
                self.update_chat(f"找到{len(code_blocks)}个代码块，打开代码编辑器...")
                self.current_code_blocks = code_blocks
                self.show_code_editor(code_blocks)
            else:
                self.update_chat("未能从响应中提取有效的代码。请尝试更明确的指令。")
                
        except Exception as e:
            self.update_chat(f"执行代码时出错: {str(e)}")
    
    def parse_visualization_request(self, code):
        """从代码中解析可视化请求并创建可视化"""
        try:
            # 尝试确定可视化类型
            viz_type = None
            x_column = None
            y_column = None
            title = "数据可视化"
            
            # 查找常见的可视化模式
            if '.plot(' in code:
                viz_type = 'line'  # 默认plot类型
                if 'kind=' in code:
                    # 尝试提取kind参数
                    kind_match = code.split('kind=')[1].split(',')[0].strip().strip("'\"")
                    viz_type = kind_match
            elif '.bar(' in code:
                viz_type = 'bar'
            elif '.pie(' in code:
                viz_type = 'pie'
            elif '.hist(' in code:
                viz_type = 'hist'
            elif '.scatter(' in code:
                viz_type = 'scatter'
            
            # 尝试提取x和y列
            if self.current_excel_df is not None and hasattr(self.current_excel_df, 'columns'):
                if 'x=' in code:
                    x_match = code.split('x=')[1].split(',')[0].strip().strip("'\"")
                    if x_match in self.current_excel_df.columns:
                        x_column = x_match
                
                if 'y=' in code:
                    y_match = code.split('y=')[1].split(',')[0].strip().strip("'\"")
                    if y_match in self.current_excel_df.columns:
                        y_column = y_match
            
            # 尝试提取标题
            if 'title=' in code:
                title_match = code.split('title=')[1].split(',')[0].strip().strip("'\"")
                title = title_match
            
            # 如果识别出可视化类型，创建可视化
            if viz_type and self.current_excel_df is not None:
                self.update_chat(f"正在创建{viz_type}可视化...")
                fig, message = create_visualization(
                    self.current_excel_df, 
                    viz_type, 
                    x_column, 
                    y_column, 
                    title
                )
                
                if fig:
                    self.display_visualization(fig)
                    self.update_chat("可视化创建成功！")
                else:
                    self.update_chat(f"创建可视化失败: {message}")
            else:
                self.update_chat("未能识别可视化请求。请尝试更明确的指令。")
                
        except Exception as e:
            self.update_chat(f"解析可视化请求时出错: {str(e)}")
    
    def display_visualization(self, fig):
        """在GUI中显示可视化图表"""
        # 清除之前的可视化
        if self.canvas:
            self.canvas.get_tk_widget().destroy()
        
        # 显示可视化区域
        if not self.viz_frame.winfo_ismapped():
            self.viz_frame.pack(padx=10, pady=5, fill="both", expand=True)
        
        # 创建新的画布
        self.canvas = FigureCanvasTkAgg(fig, master=self.viz_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill="both", expand=True)
        
        # 保存当前图表
        self.current_figure = fig
    
    def add_save_result_button(self):
        """添加保存结果按钮（如果不存在）"""
        # 检查按钮是否已存在
        for widget in self.root.winfo_children():
            if isinstance(widget, ttk.Frame) and hasattr(widget, 'save_result_btn'):
                return  # 按钮已存在
        
        # 创建保存结果按钮框架
        save_frame = ttk.Frame(self.root)
        save_frame.pack(padx=10, pady=5, fill="x")
        
        save_result_btn = ttk.Button(save_frame, text="保存处理结果", command=self.save_processed_excel)
        save_result_btn.pack(side="left", padx=5, pady=5)
        
        # 添加保存图表按钮
        save_chart_btn = ttk.Button(save_frame, text="保存图表", command=self.save_chart)
        save_chart_btn.pack(side="left", padx=5, pady=5)
        
        # 标记按钮已存在 - 使用字典属性而不是直接添加属性
        setattr(save_frame, 'save_result_btn', True)
    
    def save_processed_excel(self):
        """保存处理后的Excel文件"""
        if self.current_excel_df is None:
            self.update_chat("错误: 没有可保存的数据")
            return
            
        file_path = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            filetypes=[("Excel文件", "*.xlsx")],
            initialfile="处理结果.xlsx"
        )
        if not file_path:
            return
            
        try:
            self.current_excel_df.to_excel(file_path, index=False)
            self.update_chat(f"处理结果已保存到: {file_path}")
        except Exception as e:
            self.update_chat(f"保存Excel文件失败: {str(e)}")
    
    def save_chart(self):
        """保存当前图表"""
        if self.current_figure is None:
            self.update_chat("错误: 没有可保存的图表")
            return
            
        file_path = filedialog.asksaveasfilename(
            defaultextension=".png",
            filetypes=[("PNG图片", "*.png"), ("PDF文件", "*.pdf"), ("SVG图片", "*.svg")],
            initialfile="数据可视化.png"
        )
        if not file_path:
            return
            
        try:
            self.current_figure.savefig(file_path, dpi=300, bbox_inches='tight')
            self.update_chat(f"图表已保存到: {file_path}")
        except Exception as e:
            self.update_chat(f"保存图表失败: {str(e)}")
    
    def show_code_editor(self, code_blocks):
        """显示代码编辑窗口"""
        # 如果已经有窗口打开，先关闭它
        if self.code_editor_window and self.code_editor_window.winfo_exists():
            self.code_editor_window.destroy()
        
        # 创建新窗口
        self.code_editor_window = tk.Toplevel(self.root)
        self.code_editor_window.title("代码编辑器")
        self.code_editor_window.geometry("800x600")
        
        # 创建说明标签
        instruction_label = ttk.Label(
            self.code_editor_window, 
            text="您可以修改下面的代码，然后点击'执行'按钮运行修改后的代码。",
            wraplength=780
        )
        instruction_label.pack(padx=10, pady=5)
        
        # 创建代码编辑区域
        code_frame = ttk.Frame(self.code_editor_window)
        code_frame.pack(padx=10, pady=5, fill="both", expand=True)
        
        # 合并所有代码块
        combined_code = "\n\n# " + "-" * 30 + " 新代码块 " + "-" * 30 + "\n\n".join(code_blocks)
        
        # 创建代码编辑器
        self.code_editor = scrolledtext.ScrolledText(
            code_frame, 
            wrap=tk.NONE,  # 不自动换行
            font=("Courier New", 10)  # 使用等宽字体
        )
        self.code_editor.pack(fill="both", expand=True)
        self.code_editor.insert("1.0", combined_code)
        
        # 创建按钮区域
        button_frame = ttk.Frame(self.code_editor_window)
        button_frame.pack(padx=10, pady=10, fill="x")
        
        # 添加执行按钮
        execute_btn = ttk.Button(
            button_frame, 
            text="执行", 
            command=self.execute_edited_code
        )
        execute_btn.pack(side="right", padx=5)
        
        # 添加取消按钮
        cancel_btn = ttk.Button(
            button_frame, 
            text="取消", 
            command=self.code_editor_window.destroy
        )
        cancel_btn.pack(side="right", padx=5)
    
    def execute_edited_code(self):
        """执行编辑后的代码"""
        if self.code_editor_window and self.code_editor_window.winfo_exists():
            # 获取编辑后的代码
            edited_code = self.code_editor.get("1.0", tk.END)
            
            # 关闭编辑窗口
            self.code_editor_window.destroy()
            
            # 执行代码
            self.update_chat("正在执行修改后的代码...")
            
            try:
                # 检查DataFrame是否有效
                if self.current_excel_df is None:
                    self.update_chat("错误: 没有有效的Excel数据")
                    return
                    
                # 创建本地变量df引用数据框
                df = self.current_excel_df.copy()
                result_df = None
                viz_request = None
                
                # 检查是否包含可视化请求
                if any(viz_type in edited_code for viz_type in ['plot(', '.bar(', '.pie(', '.hist(', '.scatter(']):
                    viz_request = edited_code
                
                # 执行代码
                local_vars = {
                    'df': df, 
                    'pd': pd, 
                    'np': np, 
                    'plt': plt,
                    'DataFrame': pd.DataFrame,
                    'Series': pd.Series,
                    'datetime': __import__('datetime'),
                    'math': __import__('math'),
                    'os': os,
                    'json': json,
                    'file_path': self.current_excel_path,  # 添加文件路径变量
                    'print': print  # 添加print函数，确保代码中的print语句能正常工作
                }
                
                # 显示正在处理的文件信息
                if self.current_excel_path:
                    self.update_chat(f"正在处理文件: {os.path.basename(self.current_excel_path)}")
                
                # 执行代码
                exec(edited_code, globals(), local_vars)
                
                # 检查是否生成了新的数据框
                for var_name, var_value in local_vars.items():
                    if isinstance(var_value, pd.DataFrame) and var_name != 'df':
                        result_df = var_value
                        self.update_chat(f"发现新的DataFrame: {var_name}")
                        break
                
                # 如果没有明确的新数据框，但df被修改了，使用修改后的df
                if result_df is None and self.current_excel_df is not None and not df.equals(self.current_excel_df):
                    result_df = df
                    self.update_chat("检测到DataFrame被修改")
                
                # 处理结果
                if result_df is not None:
                    self.update_chat("数据处理完成！")
                    preview = result_df.head(10).to_string()
                    self.update_chat(f"处理结果预览 (前10行):\n{preview}")
                    
                    # 更新当前数据框
                    self.current_excel_df = result_df
                    
                    # 提供保存选项
                    self.update_chat("您可以使用'保存处理结果'按钮保存处理后的Excel文件")
                    
                    # 添加保存按钮（如果不存在）
                    self.add_save_result_button()
                
                # 处理可视化请求
                if viz_request:
                    self.parse_visualization_request(viz_request)
                    
            except Exception as e:
                self.update_chat(f"执行代码时出错: {str(e)}")
    
    def update_chat(self, message):
        """更新聊天显示"""
        self.chat_display.config(state="normal")
        self.chat_display.insert(tk.END, message + "\n")
        self.chat_display.config(state="disabled")
        self.chat_display.see(tk.END)

# 主程序
if __name__ == "__main__":
    root = tk.Tk()
    root.geometry("800x600")
    app = ChatGUI(root)
    root.mainloop()
